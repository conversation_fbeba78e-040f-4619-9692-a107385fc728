/******/ (() => { // webpackBootstrap
/******/ 	"use strict";
/******/ 	var __webpack_modules__ = ({

/***/ "./assets/js/src/admin/payment-settings.js":
/*!*************************************************!*\
  !*** ./assets/js/src/admin/payment-settings.js ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var _babel_runtime_helpers_extends__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @babel/runtime/helpers/extends */ "./node_modules/@babel/runtime/helpers/esm/extends.js");
/* harmony import */ var _wordpress_element__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @wordpress/element */ "@wordpress/element");
/* harmony import */ var _wordpress_element__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_wordpress_element__WEBPACK_IMPORTED_MODULE_1__);
/* harmony import */ var _wordpress_components__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @wordpress/components */ "@wordpress/components");
/* harmony import */ var _wordpress_components__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__);
/* harmony import */ var _wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @wordpress/i18n */ "@wordpress/i18n");
/* harmony import */ var _wordpress_i18n__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__);

/**
 * Monoova Payment Settings - React-based Admin Interface
 */





// Info Icon component with hover popover
const InfoIcon = (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_1__.memo)(({
  type
}) => {
  const [isHovered, setIsHovered] = (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_1__.useState)(false);
  const getPopoverContent = () => {
    switch (type) {
      case "label":
        return /*#__PURE__*/React.createElement("div", {
          style: {
            padding: "24px 16px",
            width: "300px"
          }
        }, /*#__PURE__*/React.createElement("div", {
          style: {
            fontSize: "14px",
            fontWeight: "500",
            marginBottom: "16px"
          }
        }, "Customize the Field of the card details"), /*#__PURE__*/React.createElement("div", {
          style: {
            backgroundColor: "#FAFAFA",
            padding: "16px 12px"
          }
        }, /*#__PURE__*/React.createElement("div", {
          style: {
            fontSize: "14px",
            fontWeight: "500",
            color: "#000000",
            marginBottom: "4px",
            margin: "-6px",
            border: "2px solid #FF4E4E",
            padding: "6px",
            borderRadius: "4px",
            display: "inline-block"
          }
        }, "Card number"), /*#__PURE__*/React.createElement("div", {
          style: {
            border: "1px solid #d1d5db",
            borderRadius: "8px",
            padding: "10px",
            fontSize: "14px",
            color: "#999"
          }
        }, "1234 5678 9012 3456")));
      case "scan_pay":
        return /*#__PURE__*/React.createElement("div", {
          style: {
            padding: "24px 16px",
            width: "300px"
          }
        }, /*#__PURE__*/React.createElement("div", {
          style: {
            fontSize: "14px",
            fontWeight: "500",
            marginBottom: "16px"
          }
        }, "Customize the Scan Pay Display elements"), /*#__PURE__*/React.createElement("div", {
          style: {
            backgroundColor: "#FAFAFA",
            padding: "16px 12px",
            textAlign: "center"
          }
        }, /*#__PURE__*/React.createElement("div", {
          style: {
            fontSize: "28px",
            fontWeight: "700",
            marginBottom: "8px"
          }
        }, /*#__PURE__*/React.createElement("span", {
          style: {
            color: "#000000"
          }
        }, "Pay "), /*#__PURE__*/React.createElement("span", {
          style: {
            color: "#2CB5C5"
          }
        }, "$10.00"))));
      case "input":
        return /*#__PURE__*/React.createElement("div", {
          style: {
            padding: "24px 16px",
            width: "300px"
          }
        }, /*#__PURE__*/React.createElement("div", {
          style: {
            fontSize: "14px",
            fontWeight: "500",
            marginBottom: "16px"
          }
        }, "Customize the Field of the card details"), /*#__PURE__*/React.createElement("div", {
          style: {
            backgroundColor: "#FAFAFA",
            padding: "16px 12px"
          }
        }, /*#__PURE__*/React.createElement("div", {
          style: {
            fontSize: "14px",
            fontWeight: "500",
            color: "#000000",
            marginBottom: "4px"
          }
        }, "Card number"), /*#__PURE__*/React.createElement("div", {
          style: {
            border: "2px solid #FF4E4E",
            borderRadius: "4px",
            padding: "6px",
            margin: "-6px"
          }
        }, /*#__PURE__*/React.createElement("div", {
          style: {
            border: "1px solid #d1d5db",
            borderRadius: "8px",
            padding: "10px",
            fontSize: "14px",
            color: "#999"
          }
        }, "1234 5678 9012 3456"))));
      case "button":
        return /*#__PURE__*/React.createElement("div", {
          style: {
            padding: "24px 16px",
            width: "300px"
          }
        }, /*#__PURE__*/React.createElement("div", {
          style: {
            backgroundColor: "#2ab5c4",
            borderRadius: "10px",
            padding: "12px 24px",
            textAlign: "center",
            fontSize: "17px",
            fontWeight: "bold",
            color: "#000000",
            cursor: "pointer"
          }
        }, "Pay"));
      default:
        return null;
    }
  };
  return /*#__PURE__*/React.createElement("div", {
    style: {
      position: "relative",
      display: "inline-block"
    }
  }, /*#__PURE__*/React.createElement("div", {
    onMouseEnter: () => setIsHovered(true),
    onMouseLeave: () => setIsHovered(false),
    style: {
      width: "13px",
      height: "13px",
      borderRadius: "50%",
      backgroundColor: "#D4D4D4",
      color: "white",
      display: "flex",
      alignItems: "center",
      justifyContent: "center",
      fontSize: "8px",
      fontWeight: "bold",
      cursor: "help"
    }
  }, "i"), isHovered && /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.Popover, {
    position: "top right",
    noArrow: false,
    onClose: () => setIsHovered(false)
  }, getPopoverContent()));
});

// Stable TextControl that prevents focus loss through proper memoization
const StableTextControl = (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_1__.memo)(({
  value,
  onChange,
  error,
  ...props
}) => {
  const className = error ? "has-error" : "";
  return /*#__PURE__*/React.createElement("div", null, /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.TextControl, (0,_babel_runtime_helpers_extends__WEBPACK_IMPORTED_MODULE_0__["default"])({}, props, {
    value: value || "",
    onChange: onChange,
    className: className,
    style: error ? {
      borderColor: "#d63638"
    } : {}
  })), error && /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.__experimentalText, {
    color: "#d63638",
    size: "12",
    style: {
      marginTop: "4px",
      display: "block"
    }
  }, error));
});

// Stable TextareaControl that prevents focus loss through proper memoization
const StableTextareaControl = (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_1__.memo)(({
  value,
  onChange,
  ...props
}) => {
  return /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.TextareaControl, (0,_babel_runtime_helpers_extends__WEBPACK_IMPORTED_MODULE_0__["default"])({}, props, {
    value: value || "",
    onChange: onChange
  }));
});

// Form field wrapper component similar to Stripe's layout
const FormField = (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_1__.memo)(({
  label,
  description,
  required = false,
  children
}) => /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.BaseControl, {
  className: "monoova-form-field"
}, /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.__experimentalVStack, {
  spacing: 2
}, /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.Flex, {
  align: "center",
  justify: "flex-start",
  gap: 1
}, /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.__experimentalText, {
  weight: "500",
  size: "14",
  color: "#1e1e1e"
}, label), required && /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.__experimentalText, {
  color: "#d63638",
  size: "14"
}, "*")), children, description && /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.__experimentalText, {
  variant: "muted",
  size: "13",
  lineHeight: "1.4"
}, description))));

// Color field component with ColorPicker popup
const ColorField = (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_1__.memo)(({
  label,
  description,
  value,
  onChange,
  disabled = false
}) => {
  const [isOpen, setIsOpen] = (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_1__.useState)(false);
  return /*#__PURE__*/React.createElement(FormField, {
    label: label,
    description: description
  }, /*#__PURE__*/React.createElement("div", {
    style: {
      position: "relative"
    }
  }, /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.Button
  //variant="secondary"
  , {
    disabled: disabled,
    onClick: () => setIsOpen(!isOpen),
    style: {
      display: "flex",
      alignItems: "center",
      justifyContent: "space-between",
      gap: "8px",
      padding: "10px",
      border: "1px solid #d1d5db",
      borderRadius: "8px",
      background: "#fff",
      cursor: disabled ? "not-allowed" : "pointer",
      width: "100%",
      height: "45px"
    }
  }, /*#__PURE__*/React.createElement("span", null, value || "#000000"), /*#__PURE__*/React.createElement("div", {
    style: {
      width: "25px",
      height: "25px",
      borderRadius: "8px",
      backgroundColor: value || "#000000",
      border: "1px solid #ddd"
    }
  })), isOpen && /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.Popover, {
    position: "bottom left",
    onClose: () => setIsOpen(false),
    noArrow: false
  }, /*#__PURE__*/React.createElement("div", {
    style: {
      padding: "16px"
    }
  }, /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.ColorPicker, {
    color: value || "#000000",
    onChange: onChange,
    enableAlpha: false
  })))));
});

// Webhook Configuration Component
const WebhookConfigurationSection = (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_1__.memo)(({
  mode,
  modeLabel,
  status,
  onCheckStatus,
  onSubscribe
}) => /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.Card, null, /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.CardBody, null, /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.__experimentalVStack, {
  spacing: 4
}, /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.__experimentalText, {
  size: "14",
  color: "#374151"
}, (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)(`Configure ${modeLabel} webhook notifications to receive real-time payment and transaction status updates from Monoova.`, "monoova-payments-for-woocommerce")), status?.validationError && /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.Notice, {
  status: "warning",
  isDismissible: false
}, /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.__experimentalText, {
  size: "14",
  color: "#B45309"
}, status.validationError)), /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.Flex, {
  align: "center",
  justify: "space-between",
  gap: 3,
  style: {
    width: "100%"
  }
}, /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.Flex, {
  align: "center",
  justify: "flex-start",
  gap: 3,
  style: {
    width: "100%"
  }
}, /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.__experimentalText, {
  weight: "600",
  size: "14"
}, (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)(`${modeLabel} webhook notifications`, "monoova-payments-for-woocommerce")), /*#__PURE__*/React.createElement("div", {
  className: `monoova-webhook-status-chip ${status?.all_active ? "active" : "inactive"}`
}, status?.isChecking ? /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.Flex, {
  align: "center",
  gap: 2
}, /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.Spinner, {
  style: {
    width: "14px",
    height: "14px",
    margin: 0
  }
}), /*#__PURE__*/React.createElement("span", null, (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("Checking", "monoova-payments-for-woocommerce"))) : status?.all_active ? (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("Active", "monoova-payments-for-woocommerce") : (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("Inactive", "monoova-payments-for-woocommerce"))), /*#__PURE__*/React.createElement("div", null, /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.Button, {
  variant: "primary",
  style: {
    justifyContent: "center"
  },
  onClick: onSubscribe,
  isBusy: status?.isConnecting,
  disabled: status?.isConnecting || status?.isChecking || status?.all_active || status?.validationError
}, status?.isConnecting ? /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.Flex, {
  align: "center",
  gap: 2
}, /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.Spinner, {
  style: {
    width: "14px",
    height: "14px",
    margin: 0
  }
}), /*#__PURE__*/React.createElement("span", null, (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("Connecting", "monoova-payments-for-woocommerce"))) : status?.all_active ? (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("Connected", "monoova-payments-for-woocommerce") : (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("Connect", "monoova-payments-for-woocommerce"))))))));

// Tab components defined outside main component to prevent recreation on re-renders
const GeneralSettingsTab = (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_1__.memo)(({
  settings,
  saveNotice,
  onChangeHandlers,
  setSaveNotice,
  validationErrors = {},
  webhookStatus,
  onCheckWebhookStatus,
  onSubscribeToWebhooks
}) => /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.__experimentalVStack, {
  spacing: 6,
  className: "monoova-general-settings-tab"
}, saveNotice && /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.Notice, {
  className: "monoova-save-notice",
  status: saveNotice.type,
  onRemove: () => setSaveNotice(null),
  isDismissible: true
}, saveNotice.message), /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.__experimentalGrid, {
  columns: 12,
  gap: 6,
  className: "monoova-settings-section"
}, /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.__experimentalVStack, {
  spacing: 3,
  style: {
    gridColumn: "span 4"
  }
}, /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.__experimentalHeading, {
  level: 3
}, (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("Basic Settings", "monoova-payments-for-woocommerce")), /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.__experimentalText, {
  variant: "muted",
  size: "14"
}, (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("Configure basic payment gateway settings.", "monoova-payments-for-woocommerce"))), /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.__experimentalVStack, {
  spacing: 4,
  style: {
    gridColumn: "span 8"
  }
}, /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.Card, null, /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.CardBody, null, /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.__experimentalVStack, {
  spacing: 4
}, /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.PanelRow, null, /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.Flex, {
  align: "center",
  justify: "flex-start",
  gap: 3,
  style: {
    width: "100%"
  }
}, /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.CheckboxControl, {
  checked: settings.enabled,
  onChange: value => {
    onChangeHandlers.enabled(value);
    // When unified gateway is disabled, also disable child gateways
    if (!value) {
      onChangeHandlers.enable_card_payments(false);
      onChangeHandlers.enable_payid_payments(false);
      onChangeHandlers.enable_payto_payments(false);
    }
  }
}), /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.__experimentalVStack, {
  spacing: 1
}, /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.__experimentalText, {
  weight: "500",
  size: "14"
}, (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("Enable Monoova Payments", "monoova-payments-for-woocommerce")), /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.__experimentalText, {
  variant: "muted",
  size: "13"
}, (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("Enable this payment gateway to accept payments.", "monoova-payments-for-woocommerce")))))))))), /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.__experimentalGrid, {
  columns: 12,
  gap: 6,
  className: "monoova-settings-section"
}, /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.__experimentalVStack, {
  spacing: 3,
  style: {
    gridColumn: "span 4"
  }
}, /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.__experimentalHeading, {
  level: 3
}, (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("Account Settings", "monoova-payments-for-woocommerce")), /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.__experimentalText, {
  variant: "muted",
  size: "14"
}, (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("Configure your Monoova account details.", "monoova-payments-for-woocommerce"))), /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.__experimentalVStack, {
  spacing: 4,
  style: {
    gridColumn: "span 8"
  }
}, /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.Card, null, /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.CardBody, null, /*#__PURE__*/React.createElement(FormField, {
  label: (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("Monoova mAccount Number", "monoova-payments-for-woocommerce"),
  description: (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("Your general Monoova mAccount number for transactions.", "monoova-payments-for-woocommerce"),
  required: true
}, /*#__PURE__*/React.createElement(StableTextControl, {
  value: settings.maccount_number || "",
  onChange: onChangeHandlers.maccount_number,
  placeholder: (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("Enter M-Account number", "monoova-payments-for-woocommerce")
})))))), /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.__experimentalGrid, {
  columns: 12,
  gap: 6,
  className: "monoova-settings-section"
}, /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.__experimentalVStack, {
  spacing: 3,
  style: {
    gridColumn: "span 4"
  }
}, /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.__experimentalHeading, {
  level: 3
}, (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("API Credentials", "monoova-payments-for-woocommerce")), /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.__experimentalText, {
  variant: "muted",
  size: "14"
}, (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("Secure API keys for connecting to Monoova services.", "monoova-payments-for-woocommerce"))), /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.__experimentalVStack, {
  spacing: 4,
  style: {
    gridColumn: "span 8"
  }
}, /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.Card, null, /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.CardBody, null, /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.__experimentalVStack, {
  spacing: 4
}, /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.__experimentalGrid, {
  columns: 2,
  gap: 4
}, /*#__PURE__*/React.createElement(FormField, {
  label: (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("Test API Key", "monoova-payments-for-woocommerce"),
  description: (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("Get your Test API key from your Monoova dashboard.", "monoova-payments-for-woocommerce")
}, /*#__PURE__*/React.createElement(StableTextControl, {
  value: settings.test_api_key || "",
  onChange: onChangeHandlers.test_api_key,
  type: "password",
  placeholder: (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("Enter test API key", "monoova-payments-for-woocommerce")
})), /*#__PURE__*/React.createElement(FormField, {
  label: (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("Live API Key", "monoova-payments-for-woocommerce"),
  description: (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("Get your Live API key from your Monoova dashboard.", "monoova-payments-for-woocommerce")
}, /*#__PURE__*/React.createElement(StableTextControl, {
  value: settings.live_api_key || "",
  onChange: onChangeHandlers.live_api_key,
  type: "password",
  placeholder: (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("Enter live API key", "monoova-payments-for-woocommerce"),
  error: validationErrors.live_api_key
})))))))), /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.__experimentalGrid, {
  columns: 12,
  gap: 6,
  className: "monoova-settings-section"
}, /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.__experimentalVStack, {
  spacing: 3,
  style: {
    gridColumn: "span 4"
  }
}, /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.__experimentalHeading, {
  level: 3
}, (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("API URLs (Advanced)", "monoova-payments-for-woocommerce")), /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.__experimentalText, {
  variant: "muted",
  size: "14"
}, (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("Override default API URLs if needed. Leave blank to use defaults.", "monoova-payments-for-woocommerce"))), /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.__experimentalVStack, {
  spacing: 4,
  style: {
    gridColumn: "span 8"
  }
}, /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.Card, null, /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.CardBody, null, /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.__experimentalVStack, {
  spacing: 4
}, /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.__experimentalGrid, {
  columns: 2,
  gap: 4
}, /*#__PURE__*/React.createElement(FormField, {
  label: (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("PayID API URL (Sandbox)", "monoova-payments-for-woocommerce")
}, /*#__PURE__*/React.createElement(StableTextControl, {
  value: settings.monoova_payments_api_url_sandbox || "https://api.m-pay.com.au",
  onChange: onChangeHandlers.monoova_payments_api_url_sandbox,
  placeholder: "https://api.m-pay.com.au"
})), /*#__PURE__*/React.createElement(FormField, {
  label: (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("PayID API URL (Live)", "monoova-payments-for-woocommerce")
}, /*#__PURE__*/React.createElement(StableTextControl, {
  value: settings.monoova_payments_api_url_live || "https://api.mpay.com.au",
  onChange: onChangeHandlers.monoova_payments_api_url_live,
  placeholder: "https://api.mpay.com.au",
  error: validationErrors.monoova_payments_api_url_live
}))), /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.__experimentalGrid, {
  columns: 2,
  gap: 4
}, /*#__PURE__*/React.createElement(FormField, {
  label: (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("Card API URL (Sandbox)", "monoova-payments-for-woocommerce")
}, /*#__PURE__*/React.createElement(StableTextControl, {
  value: settings.monoova_card_api_url_sandbox || "https://sand-api.monoova.com",
  onChange: onChangeHandlers.monoova_card_api_url_sandbox,
  placeholder: "https://sand-api.monoova.com"
})), /*#__PURE__*/React.createElement(FormField, {
  label: (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("Card API URL (Live)", "monoova-payments-for-woocommerce")
}, /*#__PURE__*/React.createElement(StableTextControl, {
  value: settings.monoova_card_api_url_live || "https://api.monoova.com",
  onChange: onChangeHandlers.monoova_card_api_url_live,
  placeholder: "https://api.monoova.com",
  error: validationErrors.monoova_card_api_url_live
})))))))), /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.__experimentalGrid, {
  columns: 12,
  gap: 6,
  className: "monoova-settings-section"
}, /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.__experimentalVStack, {
  spacing: 3,
  style: {
    gridColumn: "span 4"
  }
}, /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.__experimentalHeading, {
  level: 3
}, (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("Webhook Configuration - Sandbox mode", "monoova-payments-for-woocommerce")), /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.__experimentalText, {
  variant: "muted",
  size: "14"
}, (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("Configure SANDBOX webhook notifications for testing. You MUST subscribe to all webhook events to receive payment and transaction status notifications from Monoova.", "monoova-payments-for-woocommerce"))), /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.__experimentalVStack, {
  spacing: 4,
  style: {
    gridColumn: "span 8"
  }
}, /*#__PURE__*/React.createElement(WebhookConfigurationSection, {
  mode: "sandbox",
  modeLabel: "Sandbox",
  status: webhookStatus?.sandbox,
  onCheckStatus: () => onCheckWebhookStatus(true),
  onSubscribe: () => onSubscribeToWebhooks(true)
}))), /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.__experimentalGrid, {
  columns: 12,
  gap: 6,
  className: "monoova-settings-section"
}, /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.__experimentalVStack, {
  spacing: 3,
  style: {
    gridColumn: "span 4"
  }
}, /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.__experimentalHeading, {
  level: 3
}, (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("Webhook Configuration - Live mode", "monoova-payments-for-woocommerce")), /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.__experimentalText, {
  variant: "muted",
  size: "14"
}, (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("Configure LIVE webhook notifications for production. You MUST subscribe to all webhook events to receive payment and transaction status notifications from Monoova.", "monoova-payments-for-woocommerce"))), /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.__experimentalVStack, {
  spacing: 4,
  style: {
    gridColumn: "span 8"
  }
}, /*#__PURE__*/React.createElement(WebhookConfigurationSection, {
  mode: "live",
  modeLabel: "Live",
  status: webhookStatus?.live,
  onCheckStatus: () => onCheckWebhookStatus(false),
  onSubscribe: () => onSubscribeToWebhooks(false)
})))));

// Payment Method Option Component (Stripe-like)
const PaymentMethodOption = (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_1__.memo)(({
  label,
  description,
  icons,
  checked,
  onChange,
  disabled = false
}) => /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.PanelRow, null, /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.Flex, {
  justify: "flex-start",
  align: "center",
  gap: 3
}, /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.CheckboxControl, {
  checked: checked,
  onChange: onChange,
  disabled: disabled
}), /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.__experimentalVStack, {
  spacing: 1
}, /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.__experimentalText, {
  size: "14",
  weight: "500",
  color: disabled ? "#757575" : "#1e1e1e"
}, /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.Flex, {
  align: "center",
  justify: "flex-start",
  gap: 1
}, /*#__PURE__*/React.createElement("span", {
  style: {
    marginRight: "8px"
  }
}, label), icons && icons.map((icon, index) => /*#__PURE__*/React.createElement("img", {
  key: index,
  src: icon.src,
  alt: icon.alt,
  width: "24",
  height: "16"
})))), description && /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.__experimentalText, {
  size: "12",
  color: "#757575",
  lineHeight: "1.4"
}, description)))));
const PaymentMethodsTab = (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_1__.memo)(({
  settings,
  saveNotice,
  onChangeHandlers,
  setSaveNotice
}) => /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.__experimentalVStack, {
  spacing: 6,
  className: "monoova-payment-methods-tab"
}, saveNotice && /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.Notice, {
  status: saveNotice.type,
  onRemove: () => setSaveNotice(null)
}, saveNotice.message), /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.__experimentalGrid, {
  columns: 12,
  gap: 6,
  className: "monoova-settings-section"
}, /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.__experimentalVStack, {
  spacing: 3,
  style: {
    gridColumn: "span 4"
  }
}, /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.__experimentalHeading, {
  level: 3
}, (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("Payments accepted on checkout", "monoova-payments-for-woocommerce")), /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.__experimentalText, {
  variant: "muted",
  size: "14"
}, (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("Select payments available to customers at checkout. Based on their device type, location, and purchase history, your customers will only see the most relevant payment methods.", "monoova-payments-for-woocommerce"))), /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.__experimentalVStack, {
  spacing: 4,
  style: {
    gridColumn: "span 8"
  }
}, /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.Card, null, /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.CardBody, null, /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.__experimentalVStack, {
  spacing: 4
}, /*#__PURE__*/React.createElement(PaymentMethodOption, {
  label: (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("Credit / debit card", "monoova-payments-for-woocommerce"),
  description: (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("Let your customers pay with major credit and debit cards without leaving your store.", "monoova-payments-for-woocommerce"),
  icons: [{
    src: `${window.monoovaPluginUrl || ""}assets/images/visa.png`,
    alt: "Visa"
  }, {
    src: `${window.monoovaPluginUrl || ""}assets/images/mastercard.png`,
    alt: "Mastercard"
  }],
  checked: settings.enable_card_payments,
  onChange: onChangeHandlers.enable_card_payments
}), /*#__PURE__*/React.createElement(PaymentMethodOption, {
  label: (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("PayID / Bank Transfer", "monoova-payments-for-woocommerce"),
  description: (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("Allow customers to pay using PayID or direct bank transfer with real-time payment confirmation.", "monoova-payments-for-woocommerce"),
  icons: [{
    src: `${window.monoovaPluginUrl || ""}assets/images/payid-logo.png`,
    alt: "PayID"
  }, {
    src: `${window.monoovaPluginUrl || ""}assets/images/bank-transfer.png`,
    alt: "Bank Transfer"
  }],
  checked: settings.enable_payid_payments,
  onChange: onChangeHandlers.enable_payid_payments
}), /*#__PURE__*/React.createElement(PaymentMethodOption, {
  label: (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("PayTo", "monoova-payments-for-woocommerce"),
  description: (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("Customer approves a payment agreement.", "monoova-payments-for-woocommerce"),
  icons: [{
    src: `${window.monoovaPluginUrl || ""}assets/images/payto-logo.svg`,
    alt: "PayTo"
  }],
  checked: settings.enable_payto_payments,
  onChange: onChangeHandlers.enable_payto_payments,
  disabled: !settings.enabled
})))))), /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.__experimentalGrid, {
  columns: 12,
  gap: 6,
  className: "monoova-settings-section"
}, /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.__experimentalVStack, {
  spacing: 3,
  style: {
    gridColumn: "span 4"
  }
}, /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.__experimentalHeading, {
  level: 3
}, (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("Express checkouts", "monoova-payments-for-woocommerce")), /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.__experimentalText, {
  variant: "muted",
  size: "14"
}, (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("Let your customers use their favorite express checkout methods.", "monoova-payments-for-woocommerce"))), /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.__experimentalVStack, {
  spacing: 4,
  style: {
    gridColumn: "span 8"
  }
}, /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.Card, null, /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.CardBody, null, /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.__experimentalVStack, {
  spacing: 4
}, /*#__PURE__*/React.createElement(PaymentMethodOption, {
  label: (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("Express checkout by credit / debit card", "monoova-payments-for-woocommerce"),
  description: (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("Allow customers to skip the checkout form with saved card payment details.", "monoova-payments-for-woocommerce"),
  icons: [{
    src: `${window.monoovaPluginUrl || ""}assets/images/cards.png`,
    alt: "Express Checkout"
  }],
  checked: settings.enable_express_checkout,
  onChange: onChangeHandlers.enable_express_checkout,
  disabled: !settings.enable_card_payments
}), /*#__PURE__*/React.createElement(PaymentMethodOption, {
  label: (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("Express checkout by PayTo agreement", "monoova-payments-for-woocommerce"),
  description: (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("Allow customers to skip the checkout form with saved PayTo agreements.", "monoova-payments-for-woocommerce"),
  icons: [{
    src: `${window.monoovaPluginUrl || ""}assets/images/bank-transfer.png`,
    alt: "PayTo Express Checkout"
  }],
  checked: settings.enable_payto_express_checkout,
  onChange: onChangeHandlers.enable_payto_express_checkout,
  disabled: !settings.enable_payto_payments
}), settings.enable_express_checkout && settings.enable_payto_express_checkout && /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.Card, null, /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.CardBody, null, /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.__experimentalVStack, {
  spacing: 3
}, /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.__experimentalHeading, {
  level: 5
}, (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("Express Checkout Method Priority", "monoova-payments-for-woocommerce")), /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.__experimentalText, {
  variant: "muted",
  size: "14"
}, (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("Choose which express checkout method to show first when both Card and PayTo are available.", "monoova-payments-for-woocommerce")), /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.SelectControl, {
  value: settings.express_checkout_method_priority || "card",
  onChange: onChangeHandlers.express_checkout_method_priority,
  options: [{
    label: (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("Card Express Checkout First", "monoova-payments-for-woocommerce"),
    value: "card"
  }, {
    label: (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("PayTo Express Checkout First", "monoova-payments-for-woocommerce"),
    value: "payto"
  }]
})))))))))));
const CardSettingsTab = (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_1__.memo)(({
  settings,
  saveNotice,
  onChangeHandlers,
  setSaveNotice,
  handleSettingChange
}) => /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.__experimentalVStack, {
  spacing: 6,
  className: "monoova-card-settings-tab"
}, saveNotice && /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.Notice, {
  className: "monoova-save-notice",
  status: saveNotice.type,
  onRemove: () => setSaveNotice(null),
  isDismissible: true
}, saveNotice.message), !settings.enable_card_payments && /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.Notice, {
  status: "warning",
  isDismissible: false
}, (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("Card payments are disabled. Enable them in the Payment Methods tab to configure these settings.", "monoova-payments-for-woocommerce")), settings.card_testmode && /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.Card, {
  className: "monoova-account-status"
}, /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.CardBody, null, /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.__experimentalVStack, {
  spacing: 2
}, /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.__experimentalText, {
  weight: "500",
  size: "14"
}, (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("Test Mode", "monoova-payments-for-woocommerce")), /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.__experimentalText, {
  size: "13"
}, (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("When enabled, card payment methods powered by Monoova will appear on checkout in test mode. No live transactions are processed.", "monoova-payments-for-woocommerce"))))), /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.__experimentalGrid, {
  columns: 12,
  gap: 6,
  className: "monoova-settings-section"
}, /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.__experimentalVStack, {
  spacing: 3,
  style: {
    gridColumn: "span 4"
  }
}, /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.__experimentalHeading, {
  level: 3
}, (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("Basic Information", "monoova-payments-for-woocommerce")), /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.__experimentalText, {
  variant: "muted",
  size: "14"
}, (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("Configure how this payment method appears to customers.", "monoova-payments-for-woocommerce"))), /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.__experimentalVStack, {
  spacing: 4,
  style: {
    gridColumn: "span 8"
  }
}, /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.Card, null, /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.CardBody, null, /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.__experimentalVStack, {
  spacing: 4
}, /*#__PURE__*/React.createElement(FormField, {
  label: (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("Title", "monoova-payments-for-woocommerce"),
  description: (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("This controls the title which the user sees during checkout.", "monoova-payments-for-woocommerce"),
  required: true
}, /*#__PURE__*/React.createElement(StableTextControl, {
  value: settings.card_title || "",
  onChange: onChangeHandlers.card_title,
  disabled: !settings.enable_card_payments,
  placeholder: (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("Enter card payment method title", "monoova-payments-for-woocommerce")
})), /*#__PURE__*/React.createElement(FormField, {
  label: (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("Description", "monoova-payments-for-woocommerce"),
  description: (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("This controls the description which the user sees during checkout.", "monoova-payments-for-woocommerce")
}, /*#__PURE__*/React.createElement(StableTextareaControl, {
  value: settings.card_description || "",
  onChange: onChangeHandlers.card_description,
  disabled: !settings.enable_card_payments,
  rows: 3,
  placeholder: (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("Enter card payment method description", "monoova-payments-for-woocommerce")
}))))))), /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.__experimentalGrid, {
  columns: 12,
  gap: 6,
  className: "monoova-settings-section"
}, /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.__experimentalVStack, {
  spacing: 3,
  style: {
    gridColumn: "span 4"
  }
}, /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.__experimentalHeading, {
  level: 3
}, (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("Card Settings", "monoova-payments-for-woocommerce")), /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.__experimentalText, {
  variant: "muted",
  size: "14"
}, (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("Configure test mode and logging for card payments.", "monoova-payments-for-woocommerce"))), /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.__experimentalVStack, {
  spacing: 4,
  style: {
    gridColumn: "span 8"
  }
}, /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.Card, null, /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.CardBody, null, /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.__experimentalVStack, {
  spacing: 4
}, /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.__experimentalGrid, {
  columns: 2,
  gap: 4
}, /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.PanelRow, null, /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.Flex, {
  align: "center",
  justify: "flex-start",
  gap: 3,
  style: {
    width: "100%"
  }
}, /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.CheckboxControl, {
  checked: settings.card_testmode,
  disabled: !settings.enable_card_payments,
  onChange: onChangeHandlers.card_testmode
}), /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.__experimentalVStack, {
  spacing: 1
}, /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.__experimentalText, {
  weight: "500",
  size: "14"
}, (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("Test Mode", "monoova-payments-for-woocommerce")), /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.__experimentalText, {
  variant: "muted",
  size: "13"
}, (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("Process card payments using test API keys", "monoova-payments-for-woocommerce"))))), /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.PanelRow, null, /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.Flex, {
  align: "center",
  justify: "flex-start",
  gap: 3,
  style: {
    width: "100%"
  }
}, /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.CheckboxControl, {
  checked: settings.card_debug,
  disabled: !settings.enable_card_payments,
  onChange: onChangeHandlers.card_debug
}), /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.__experimentalVStack, {
  spacing: 1
}, /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.__experimentalText, {
  weight: "500",
  size: "14"
}, (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("Enable logging", "monoova-payments-for-woocommerce")), /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.__experimentalText, {
  variant: "muted",
  size: "13"
}, (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("Log card payment events for debugging purposes", "monoova-payments-for-woocommerce"))))))))))), /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.__experimentalGrid, {
  columns: 12,
  gap: 6,
  className: "monoova-settings-section"
}, /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.__experimentalVStack, {
  spacing: 3,
  style: {
    gridColumn: "span 4"
  }
}, /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.__experimentalHeading, {
  level: 3
}, (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("Payment Processing", "monoova-payments-for-woocommerce")), /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.__experimentalText, {
  variant: "muted",
  size: "14"
}, (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("Configure how card payments are processed and handled.", "monoova-payments-for-woocommerce"))), /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.__experimentalVStack, {
  spacing: 4,
  style: {
    gridColumn: "span 8"
  }
}, /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.Card, null, /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.CardBody, null, /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.__experimentalVStack, {
  spacing: 4
}, /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.PanelRow, null, /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.Flex, {
  align: "center",
  justify: "flex-start",
  gap: 3,
  style: {
    width: "100%"
  }
}, /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.CheckboxControl, {
  checked: settings.capture,
  disabled: !settings.enable_card_payments,
  onChange: onChangeHandlers.capture
}), /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.__experimentalVStack, {
  spacing: 1
}, /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.__experimentalText, {
  weight: "500",
  size: "14"
}, (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("Capture payments immediately", "monoova-payments-for-woocommerce")), /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.__experimentalText, {
  variant: "muted",
  size: "13"
}, (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("Capture the payment immediately when the order is placed", "monoova-payments-for-woocommerce"))))), /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.PanelRow, null, /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.Flex, {
  align: "center",
  justify: "flex-start",
  gap: 3,
  style: {
    width: "100%"
  }
}, /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.CheckboxControl, {
  checked: settings.saved_cards,
  disabled: !settings.enable_card_payments,
  onChange: onChangeHandlers.saved_cards
}), /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.__experimentalVStack, {
  spacing: 1
}, /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.__experimentalText, {
  weight: "500",
  size: "14"
}, (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("Enable saved cards", "monoova-payments-for-woocommerce")), /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.__experimentalText, {
  variant: "muted",
  size: "13"
}, (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("Allow customers to save payment methods for future purchases", "monoova-payments-for-woocommerce"))))), /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.__experimentalDivider, null), /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.PanelRow, null, /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.Flex, {
  align: "center",
  justify: "flex-start",
  gap: 3,
  style: {
    width: "100%"
  }
}, /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.CheckboxControl, {
  checked: settings.apply_surcharge,
  disabled: !settings.enable_card_payments,
  onChange: onChangeHandlers.apply_surcharge
}), /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.__experimentalVStack, {
  spacing: 1,
  style: {
    flexGrow: 1
  }
}, /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.__experimentalText, {
  weight: "500",
  size: "14"
}, (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("Apply surcharge", "monoova-payments-for-woocommerce")), /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.__experimentalText, {
  variant: "muted",
  size: "13"
}, (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("Add a surcharge to card payments to cover processing fees", "monoova-payments-for-woocommerce"))), settings.apply_surcharge && /*#__PURE__*/React.createElement("div", {
  style: {
    width: "120px"
  }
}, /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.TextControl, {
  value: settings.surcharge_amount,
  disabled: !settings.enable_card_payments,
  onChange: value => handleSettingChange("surcharge_amount", parseFloat(value) || 0),
  type: "number",
  min: 0,
  max: 10,
  step: 0.01
}))))))))), /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.__experimentalGrid, {
  columns: 12,
  gap: 6,
  className: "monoova-settings-section"
}, /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.__experimentalVStack, {
  spacing: 3,
  style: {
    gridColumn: "span 4"
  }
}, /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.__experimentalHeading, {
  level: 3
}, (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("Security & Wallet Settings", "monoova-payments-for-woocommerce")), /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.__experimentalText, {
  variant: "muted",
  size: "14"
}, (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("Configure security features and wallet payment options.", "monoova-payments-for-woocommerce"))), /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.__experimentalVStack, {
  spacing: 4,
  style: {
    gridColumn: "span 8"
  }
}, /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.Card, null, /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.CardBody, null, /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.__experimentalVStack, {
  spacing: 4
}, /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.__experimentalGrid, {
  columns: 2,
  gap: 4
}, /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.PanelRow, null, /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.Flex, {
  align: "center",
  justify: "flex-start",
  gap: 3,
  style: {
    width: "100%"
  }
}, /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.CheckboxControl, {
  checked: settings.enable_apple_pay,
  disabled: !settings.enable_card_payments,
  onChange: onChangeHandlers.enable_apple_pay
}), /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.__experimentalVStack, {
  spacing: 1
}, /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.__experimentalText, {
  weight: "500",
  size: "14"
}, (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("Enable Apple Pay", "monoova-payments-for-woocommerce")), /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.__experimentalText, {
  variant: "muted",
  size: "13"
}, (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("Allow customers to pay using Apple Pay on supported devices", "monoova-payments-for-woocommerce"))))), /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.PanelRow, null, /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.Flex, {
  align: "center",
  justify: "flex-start",
  gap: 3,
  style: {
    width: "100%"
  }
}, /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.CheckboxControl, {
  checked: settings.enable_google_pay,
  disabled: !settings.enable_card_payments,
  onChange: onChangeHandlers.enable_google_pay
}), /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.__experimentalVStack, {
  spacing: 1
}, /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.__experimentalText, {
  weight: "500",
  size: "14"
}, (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("Enable Google Pay", "monoova-payments-for-woocommerce")), /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.__experimentalText, {
  variant: "muted",
  size: "13"
}, (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("Allow customers to pay using Google Pay on supported devices", "monoova-payments-for-woocommerce")))))), /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.__experimentalDivider, null), /*#__PURE__*/React.createElement(FormField, {
  label: (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("Order button text", "monoova-payments-for-woocommerce"),
  description: (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("Customize the text displayed on the payment button.", "monoova-payments-for-woocommerce")
}, /*#__PURE__*/React.createElement(StableTextControl, {
  value: settings.order_button_text || "",
  onChange: onChangeHandlers.order_button_text,
  disabled: !settings.enable_card_payments,
  placeholder: (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("Pay with Card", "monoova-payments-for-woocommerce")
}))))))), /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.__experimentalGrid, {
  columns: 12,
  gap: 6,
  className: "monoova-settings-section"
}, /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.__experimentalVStack, {
  spacing: 3,
  style: {
    gridColumn: "span 4"
  }
}, /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.__experimentalHeading, {
  level: 3
}, (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("Checkout UI Style Settings", "monoova-payments-for-woocommerce")), /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.__experimentalText, {
  variant: "muted",
  size: "14"
}, (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("Customize the appearance of the checkout form fields and buttons.", "monoova-payments-for-woocommerce"))), /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.__experimentalVStack, {
  spacing: 4,
  style: {
    gridColumn: "span 8"
  }
}, /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.Card, null, /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.CardBody, null, /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.__experimentalVStack, {
  spacing: 4
}, /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.Flex, {
  align: "center",
  justify: "flex-start"
}, /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.__experimentalText, {
  weight: "500",
  size: "15"
}, (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("Label Input Fields Of Card Details", "monoova-payments-for-woocommerce")), /*#__PURE__*/React.createElement(InfoIcon, {
  type: "label"
})), /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.__experimentalGrid, {
  columns: 12,
  gap: 4,
  style: {
    gap: "16px"
  }
}, /*#__PURE__*/React.createElement("div", {
  style: {
    gridColumn: "span 7"
  }
}, /*#__PURE__*/React.createElement(FormField, {
  label: (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("Font family", "monoova-payments-for-woocommerce")
}, /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.SelectControl, {
  value: settings.checkout_ui_styles?.input_label?.font_family || "Helvetica, Arial, sans-serif",
  onChange: value => handleSettingChange("checkout_ui_styles", {
    ...settings.checkout_ui_styles,
    input_label: {
      ...settings.checkout_ui_styles?.input_label,
      font_family: value
    }
  }),
  disabled: !settings.enable_card_payments,
  options: [{
    label: "Inter",
    value: "Inter"
  }, {
    label: "Helvetica",
    value: "Helvetica, Arial, sans-serif"
  }, {
    label: "Arial",
    value: "Arial, sans-serif"
  }, {
    label: "Times New Roman",
    value: "Times New Roman, serif"
  }, {
    label: "Courier New",
    value: "Courier New, monospace"
  }]
}))), /*#__PURE__*/React.createElement("div", {
  style: {
    gridColumn: "span 3"
  }
}, /*#__PURE__*/React.createElement(FormField, {
  label: (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("Font weight", "monoova-payments-for-woocommerce")
}, /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.SelectControl, {
  value: settings.checkout_ui_styles?.input_label?.font_weight || "normal",
  onChange: value => handleSettingChange("checkout_ui_styles", {
    ...settings.checkout_ui_styles,
    input_label: {
      ...settings.checkout_ui_styles?.input_label,
      font_weight: value
    }
  }),
  disabled: !settings.enable_card_payments,
  options: [{
    label: "Regular",
    value: "normal"
  }, {
    label: "Bold",
    value: "bold"
  }, {
    label: "Light",
    value: "300"
  }, {
    label: "Medium",
    value: "500"
  }, {
    label: "Semi Bold",
    value: "600"
  }]
}))), /*#__PURE__*/React.createElement("div", {
  style: {
    gridColumn: "span 2"
  }
}, /*#__PURE__*/React.createElement(FormField, {
  label: (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("Font size", "monoova-payments-for-woocommerce")
}, /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.SelectControl, {
  value: settings.checkout_ui_styles?.input_label?.font_size || "14px",
  onChange: value => handleSettingChange("checkout_ui_styles", {
    ...settings.checkout_ui_styles,
    input_label: {
      ...settings.checkout_ui_styles?.input_label,
      font_size: value
    }
  }),
  disabled: !settings.enable_card_payments,
  options: [{
    label: "12px",
    value: "12px"
  }, {
    label: "14px",
    value: "14px"
  }, {
    label: "16px",
    value: "16px"
  }, {
    label: "18px",
    value: "18px"
  }, {
    label: "20px",
    value: "20px"
  }]
})))), /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.__experimentalGrid, {
  columns: 12,
  gap: 4,
  style: {
    gap: "16px"
  }
}, /*#__PURE__*/React.createElement("div", {
  style: {
    gridColumn: "span 3"
  }
}, /*#__PURE__*/React.createElement(ColorField, {
  label: (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("Text color", "monoova-payments-for-woocommerce"),
  value: settings.checkout_ui_styles?.input_label?.color || "#000000",
  onChange: value => handleSettingChange("checkout_ui_styles", {
    ...settings.checkout_ui_styles,
    input_label: {
      ...settings.checkout_ui_styles?.input_label,
      color: value
    }
  }),
  disabled: !settings.enable_card_payments
})))))), /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.Card, null, /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.CardBody, null, /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.__experimentalVStack, {
  spacing: 4
}, /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.Flex, {
  align: "center",
  justify: "flex-start"
}, /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.__experimentalText, {
  weight: "500",
  size: "15"
}, (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("Input Fields Of Card Details", "monoova-payments-for-woocommerce")), /*#__PURE__*/React.createElement(InfoIcon, {
  type: "input"
})), /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.__experimentalGrid, {
  columns: 12,
  gap: 4,
  style: {
    gap: "16px"
  }
}, /*#__PURE__*/React.createElement("div", {
  style: {
    gridColumn: "span 7"
  }
}, /*#__PURE__*/React.createElement(FormField, {
  label: (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("Font family", "monoova-payments-for-woocommerce")
}, /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.SelectControl, {
  value: settings.checkout_ui_styles?.input?.font_family || "Helvetica, Arial, sans-serif",
  onChange: value => handleSettingChange("checkout_ui_styles", {
    ...settings.checkout_ui_styles,
    input: {
      ...settings.checkout_ui_styles?.input,
      font_family: value
    }
  }),
  disabled: !settings.enable_card_payments,
  options: [{
    label: "Inter",
    value: "Inter"
  }, {
    label: "Helvetica",
    value: "Helvetica, Arial, sans-serif"
  }, {
    label: "Arial",
    value: "Arial, sans-serif"
  }, {
    label: "Times New Roman",
    value: "Times New Roman, serif"
  }, {
    label: "Courier New",
    value: "Courier New, monospace"
  }]
}))), /*#__PURE__*/React.createElement("div", {
  style: {
    gridColumn: "span 3"
  }
}, /*#__PURE__*/React.createElement(FormField, {
  label: (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("Font weight", "monoova-payments-for-woocommerce")
}, /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.SelectControl, {
  value: settings.checkout_ui_styles?.input?.font_weight || "normal",
  onChange: value => handleSettingChange("checkout_ui_styles", {
    ...settings.checkout_ui_styles,
    input: {
      ...settings.checkout_ui_styles?.input,
      font_weight: value
    }
  }),
  disabled: !settings.enable_card_payments,
  options: [{
    label: "Regular",
    value: "normal"
  }, {
    label: "Bold",
    value: "bold"
  }, {
    label: "Light",
    value: "300"
  }, {
    label: "Medium",
    value: "500"
  }, {
    label: "Semi Bold",
    value: "600"
  }]
}))), /*#__PURE__*/React.createElement("div", {
  style: {
    gridColumn: "span 2"
  }
}, /*#__PURE__*/React.createElement(FormField, {
  label: (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("Font size", "monoova-payments-for-woocommerce")
}, /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.SelectControl, {
  value: settings.checkout_ui_styles?.input?.font_size || "14px",
  onChange: value => handleSettingChange("checkout_ui_styles", {
    ...settings.checkout_ui_styles,
    input: {
      ...settings.checkout_ui_styles?.input,
      font_size: value
    }
  }),
  disabled: !settings.enable_card_payments,
  options: [{
    label: "12px",
    value: "12px"
  }, {
    label: "14px",
    value: "14px"
  }, {
    label: "16px",
    value: "16px"
  }, {
    label: "18px",
    value: "18px"
  }, {
    label: "20px",
    value: "20px"
  }]
})))), /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.__experimentalGrid, {
  columns: 12,
  gap: 4,
  style: {
    gap: "16px"
  }
}, /*#__PURE__*/React.createElement("div", {
  style: {
    gridColumn: "span 3"
  }
}, /*#__PURE__*/React.createElement(ColorField, {
  label: (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("Background color", "monoova-payments-for-woocommerce"),
  value: settings.checkout_ui_styles?.input?.background_color || "#FAFAFA",
  onChange: value => handleSettingChange("checkout_ui_styles", {
    ...settings.checkout_ui_styles,
    input: {
      ...settings.checkout_ui_styles?.input,
      background_color: value
    }
  }),
  disabled: !settings.enable_card_payments
})), /*#__PURE__*/React.createElement("div", {
  style: {
    gridColumn: "span 3"
  }
}, /*#__PURE__*/React.createElement(ColorField, {
  label: (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("Border color", "monoova-payments-for-woocommerce"),
  value: settings.checkout_ui_styles?.input?.border_color || "#E8E8E8",
  onChange: value => handleSettingChange("checkout_ui_styles", {
    ...settings.checkout_ui_styles,
    input: {
      ...settings.checkout_ui_styles?.input,
      border_color: value
    }
  }),
  disabled: !settings.enable_card_payments
})), /*#__PURE__*/React.createElement("div", {
  style: {
    gridColumn: "span 3"
  }
}, /*#__PURE__*/React.createElement(ColorField, {
  label: (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("Text color", "monoova-payments-for-woocommerce"),
  value: settings.checkout_ui_styles?.input?.text_color || "#000000",
  onChange: value => handleSettingChange("checkout_ui_styles", {
    ...settings.checkout_ui_styles,
    input: {
      ...settings.checkout_ui_styles?.input,
      text_color: value
    }
  }),
  disabled: !settings.enable_card_payments
})), /*#__PURE__*/React.createElement("div", {
  style: {
    gridColumn: "span 3"
  }
}, /*#__PURE__*/React.createElement(FormField, {
  label: (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("Border radius", "monoova-payments-for-woocommerce")
}, /*#__PURE__*/React.createElement(StableTextControl, {
  value: settings.checkout_ui_styles?.input?.border_radius || "8px",
  onChange: value => handleSettingChange("checkout_ui_styles", {
    ...settings.checkout_ui_styles,
    input: {
      ...settings.checkout_ui_styles?.input,
      border_radius: value
    }
  }),
  disabled: !settings.enable_card_payments,
  style: {
    borderRadius: "8px",
    padding: "10px",
    borderColor: "#D0D5DD"
  },
  placeholder: "8px"
}))))))), /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.Card, null, /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.CardBody, null, /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.__experimentalVStack, {
  spacing: 4
}, /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.Flex, {
  align: "center",
  justify: "flex-start"
}, /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.__experimentalText, {
  weight: "500",
  size: "15"
}, (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("Pay Button", "monoova-payments-for-woocommerce")), /*#__PURE__*/React.createElement(InfoIcon, {
  type: "button"
})), /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.__experimentalGrid, {
  columns: 12,
  gap: 4,
  style: {
    gap: "16px"
  }
}, /*#__PURE__*/React.createElement("div", {
  style: {
    gridColumn: "span 7"
  }
}, /*#__PURE__*/React.createElement(FormField, {
  label: (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("Font family", "monoova-payments-for-woocommerce")
}, /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.SelectControl, {
  value: settings.checkout_ui_styles?.submit_button?.font_family || "Helvetica, Arial, sans-serif",
  onChange: value => handleSettingChange("checkout_ui_styles", {
    ...settings.checkout_ui_styles,
    submit_button: {
      ...settings.checkout_ui_styles?.submit_button,
      font_family: value
    }
  }),
  disabled: !settings.enable_card_payments,
  options: [{
    label: "Inter",
    value: "Inter"
  }, {
    label: "Helvetica",
    value: "Helvetica, Arial, sans-serif"
  }, {
    label: "Arial",
    value: "Arial, sans-serif"
  }, {
    label: "Times New Roman",
    value: "Times New Roman, serif"
  }, {
    label: "Courier New",
    value: "Courier New, monospace"
  }]
}))), /*#__PURE__*/React.createElement("div", {
  style: {
    gridColumn: "span 3"
  }
}, /*#__PURE__*/React.createElement(FormField, {
  label: (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("Font weight", "monoova-payments-for-woocommerce")
}, /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.SelectControl, {
  value: settings.checkout_ui_styles?.submit_button?.font_weight || "bold",
  onChange: value => handleSettingChange("checkout_ui_styles", {
    ...settings.checkout_ui_styles,
    submit_button: {
      ...settings.checkout_ui_styles?.submit_button,
      font_weight: value
    }
  }),
  disabled: !settings.enable_card_payments,
  options: [{
    label: "Regular",
    value: "normal"
  }, {
    label: "Bold",
    value: "bold"
  }, {
    label: "Light",
    value: "300"
  }, {
    label: "Medium",
    value: "500"
  }, {
    label: "Semi Bold",
    value: "600"
  }]
}))), /*#__PURE__*/React.createElement("div", {
  style: {
    gridColumn: "span 2"
  }
}, /*#__PURE__*/React.createElement(FormField, {
  label: (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("Font size", "monoova-payments-for-woocommerce")
}, /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.SelectControl, {
  value: settings.checkout_ui_styles?.submit_button?.font_size || "17px",
  onChange: value => handleSettingChange("checkout_ui_styles", {
    ...settings.checkout_ui_styles,
    submit_button: {
      ...settings.checkout_ui_styles?.submit_button,
      font_size: value
    }
  }),
  disabled: !settings.enable_card_payments,
  options: [{
    label: "14px",
    value: "14px"
  }, {
    label: "16px",
    value: "16px"
  }, {
    label: "17px",
    value: "17px"
  }, {
    label: "18px",
    value: "18px"
  }, {
    label: "20px",
    value: "20px"
  }]
})))), /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.__experimentalGrid, {
  columns: 12,
  gap: 4,
  style: {
    gap: "16px"
  }
}, /*#__PURE__*/React.createElement("div", {
  style: {
    gridColumn: "span 3"
  }
}, /*#__PURE__*/React.createElement(ColorField, {
  label: (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("Background color", "monoova-payments-for-woocommerce"),
  value: settings.checkout_ui_styles?.submit_button?.background || "#2ab5c4",
  onChange: value => handleSettingChange("checkout_ui_styles", {
    ...settings.checkout_ui_styles,
    submit_button: {
      ...settings.checkout_ui_styles?.submit_button,
      background: value
    }
  }),
  disabled: !settings.enable_card_payments
})), /*#__PURE__*/React.createElement("div", {
  style: {
    gridColumn: "span 3"
  }
}, /*#__PURE__*/React.createElement(ColorField, {
  label: (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("Border color", "monoova-payments-for-woocommerce"),
  value: settings.checkout_ui_styles?.submit_button?.border_color || "#2ab5c4",
  onChange: value => handleSettingChange("checkout_ui_styles", {
    ...settings.checkout_ui_styles,
    submit_button: {
      ...settings.checkout_ui_styles?.submit_button,
      border_color: value
    }
  }),
  disabled: !settings.enable_card_payments
})), /*#__PURE__*/React.createElement("div", {
  style: {
    gridColumn: "span 3"
  }
}, /*#__PURE__*/React.createElement(ColorField, {
  label: (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("Text color", "monoova-payments-for-woocommerce"),
  value: settings.checkout_ui_styles?.submit_button?.text_color || "#000000",
  onChange: value => handleSettingChange("checkout_ui_styles", {
    ...settings.checkout_ui_styles,
    submit_button: {
      ...settings.checkout_ui_styles?.submit_button,
      text_color: value
    }
  }),
  disabled: !settings.enable_card_payments
})), /*#__PURE__*/React.createElement("div", {
  style: {
    gridColumn: "span 3"
  }
}, /*#__PURE__*/React.createElement(FormField, {
  label: (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("Border radius", "monoova-payments-for-woocommerce")
}, /*#__PURE__*/React.createElement(StableTextControl, {
  value: settings.checkout_ui_styles?.submit_button?.border_radius || "10px",
  onChange: value => handleSettingChange("checkout_ui_styles", {
    ...settings.checkout_ui_styles,
    submit_button: {
      ...settings.checkout_ui_styles?.submit_button,
      border_radius: value
    }
  }),
  disabled: !settings.enable_card_payments,
  style: {
    borderRadius: "8px",
    padding: "10px",
    borderColor: "#D0D5DD"
  },
  placeholder: "10px"
})))))))))));
const PayIDSettingsTab = (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_1__.memo)(({
  settings,
  saveNotice,
  onChangeHandlers,
  setSaveNotice,
  onGenerateAutomatcher,
  isGenerating
}) => /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.__experimentalVStack, {
  spacing: 6,
  className: "monoova-payid-settings-tab"
}, saveNotice && /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.Notice, {
  className: "monoova-save-notice",
  status: saveNotice.type,
  onRemove: () => setSaveNotice(null),
  isDismissible: true
}, saveNotice.message), !settings.enable_payid_payments && /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.Notice, {
  status: "warning",
  isDismissible: false
}, (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("PayID payments are disabled. Enable them in the Payment Methods tab to configure these settings.", "monoova-payments-for-woocommerce")), /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.__experimentalGrid, {
  columns: 12,
  gap: 6,
  className: "monoova-settings-section"
}, /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.__experimentalVStack, {
  spacing: 3,
  style: {
    gridColumn: "span 4"
  }
}, /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.__experimentalHeading, {
  level: 3
}, (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("Basic Information", "monoova-payments-for-woocommerce")), /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.__experimentalText, {
  variant: "muted",
  size: "14"
}, (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("Configure how this payment method appears to customers.", "monoova-payments-for-woocommerce"))), /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.__experimentalVStack, {
  spacing: 4,
  style: {
    gridColumn: "span 8"
  }
}, /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.Card, null, /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.CardBody, null, /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.__experimentalVStack, {
  spacing: 4
}, /*#__PURE__*/React.createElement(FormField, {
  label: (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("Title", "monoova-payments-for-woocommerce"),
  description: (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("This controls the title which the user sees during checkout.", "monoova-payments-for-woocommerce"),
  required: true
}, /*#__PURE__*/React.createElement(StableTextControl, {
  value: settings.payid_title || "",
  onChange: onChangeHandlers.payid_title,
  disabled: !settings.enable_payid_payments,
  placeholder: (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("Enter PayID payment method title", "monoova-payments-for-woocommerce")
})), /*#__PURE__*/React.createElement(FormField, {
  label: (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("Description", "monoova-payments-for-woocommerce"),
  description: (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("This controls the description which the user sees during checkout.", "monoova-payments-for-woocommerce")
}, /*#__PURE__*/React.createElement(StableTextareaControl, {
  value: settings.payid_description || "",
  onChange: onChangeHandlers.payid_description,
  disabled: !settings.enable_payid_payments,
  rows: 3,
  placeholder: (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("Enter PayID payment method description", "monoova-payments-for-woocommerce")
}))))))), /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.__experimentalGrid, {
  columns: 12,
  gap: 6,
  className: "monoova-settings-section"
}, /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.__experimentalVStack, {
  spacing: 3,
  style: {
    gridColumn: "span 4"
  }
}, /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.__experimentalHeading, {
  level: 3
}, (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("PayID Settings", "monoova-payments-for-woocommerce")), /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.__experimentalText, {
  variant: "muted",
  size: "14"
}, (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("Configure test mode and logging for PayID payments.", "monoova-payments-for-woocommerce"))), /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.__experimentalVStack, {
  spacing: 4,
  style: {
    gridColumn: "span 8"
  }
}, /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.Card, null, /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.CardBody, null, /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.__experimentalVStack, {
  spacing: 4
}, /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.__experimentalGrid, {
  columns: 2,
  gap: 4
}, /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.PanelRow, null, /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.Flex, {
  align: "center",
  justify: "flex-start",
  gap: 3,
  style: {
    width: "100%"
  }
}, /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.CheckboxControl, {
  checked: settings.payid_testmode,
  disabled: !settings.enable_payid_payments,
  onChange: onChangeHandlers.payid_testmode
}), /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.__experimentalVStack, {
  spacing: 1
}, /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.__experimentalText, {
  weight: "500",
  size: "14"
}, (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("Test Mode", "monoova-payments-for-woocommerce")), /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.__experimentalText, {
  variant: "muted",
  size: "13"
}, (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("Process PayID payments using test API keys", "monoova-payments-for-woocommerce"))))), /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.PanelRow, null, /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.Flex, {
  align: "center",
  justify: "flex-start",
  gap: 3,
  style: {
    width: "100%"
  }
}, /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.CheckboxControl, {
  checked: settings.payid_debug,
  disabled: !settings.enable_payid_payments,
  onChange: onChangeHandlers.payid_debug
}), /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.__experimentalVStack, {
  spacing: 1
}, /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.__experimentalText, {
  weight: "500",
  size: "14"
}, (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("Enable logging", "monoova-payments-for-woocommerce")), /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.__experimentalText, {
  variant: "muted",
  size: "13"
}, (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("Log PayID payment events for debugging purposes", "monoova-payments-for-woocommerce"))))))))))), /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.__experimentalGrid, {
  columns: 12,
  gap: 6,
  className: "monoova-settings-section"
}, /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.__experimentalVStack, {
  spacing: 3,
  style: {
    gridColumn: "span 4"
  }
}, /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.__experimentalHeading, {
  level: 3
}, (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("Payment Options", "monoova-payments-for-woocommerce")), /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.__experimentalText, {
  variant: "muted",
  size: "14"
}, (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("Configure payment types, expiry settings, and customer instructions.", "monoova-payments-for-woocommerce"))), /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.__experimentalVStack, {
  spacing: 4,
  style: {
    gridColumn: "span 8"
  }
}, /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.Card, null, /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.CardBody, null, /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.__experimentalVStack, {
  spacing: 4
}, /*#__PURE__*/React.createElement(FormField, {
  label: (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("Account Name", "monoova-payments-for-woocommerce"),
  description: (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("The account name to use when generating the store-wide Automatcher account.", "monoova-payments-for-woocommerce"),
  required: true
}, /*#__PURE__*/React.createElement(StableTextControl, {
  value: settings.account_name || "",
  onChange: onChangeHandlers.account_name,
  disabled: !settings.enable_payid_payments,
  placeholder: (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("e.g. Your Store Name", "monoova-payments-for-woocommerce")
})), /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.__experimentalGrid, {
  columns: 2,
  gap: 4
}, /*#__PURE__*/React.createElement(FormField, {
  label: (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("Store BSB", "monoova-payments-for-woocommerce")
}, /*#__PURE__*/React.createElement(StableTextControl, {
  value: settings.static_bsb || "",
  readOnly: true,
  disabled: !settings.enable_payid_payments,
  placeholder: (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("Generated by Monoova", "monoova-payments-for-woocommerce")
})), /*#__PURE__*/React.createElement(FormField, {
  label: (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("Store Account Number", "monoova-payments-for-woocommerce")
}, /*#__PURE__*/React.createElement(StableTextControl, {
  value: settings.static_account_number || "",
  readOnly: true,
  disabled: !settings.enable_payid_payments,
  placeholder: (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("Generated by Monoova", "monoova-payments-for-woocommerce")
}))), /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.Button, {
  variant: "secondary",
  onClick: onGenerateAutomatcher,
  isBusy: isGenerating,
  disabled: !settings.enable_payid_payments || isGenerating
}, (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("Generate / Replace Store Automatcher Account", "monoova-payments-for-woocommerce")), /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.__experimentalText, {
  variant: "muted",
  size: "13"
}, (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("Note: It may take up to 5 minutes for a newly generated account to become fully active for receiving payments.", "monoova-payments-for-woocommerce")), /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.__experimentalDivider, null), /*#__PURE__*/React.createElement(FormField, {
  label: (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("Payment types", "monoova-payments-for-woocommerce"),
  description: (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("Select which payment types to accept.", "monoova-payments-for-woocommerce")
}, /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.SelectControl, {
  multiple: true,
  value: settings.payment_types,
  onChange: onChangeHandlers.payment_types,
  disabled: !settings.enable_payid_payments,
  options: [{
    label: (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("PayID", "monoova-payments-for-woocommerce"),
    value: "payid"
  }, {
    label: (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("Bank Transfer", "monoova-payments-for-woocommerce"),
    value: "bank_transfer"
  }]
})), /*#__PURE__*/React.createElement(FormField, {
  label: (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("Payment expiry (hours)", "monoova-payments-for-woocommerce"),
  description: (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("Number of hours before payment instructions expire.", "monoova-payments-for-woocommerce")
}, /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.TextControl, {
  value: settings.expire_hours,
  onChange: onChangeHandlers.expire_hours,
  type: "number",
  min: 1,
  max: 168,
  disabled: !settings.enable_payid_payments
})), /*#__PURE__*/React.createElement(FormField, {
  label: (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("Payment Instructions", "monoova-payments-for-woocommerce"),
  description: (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("Additional instructions to show customers about PayID/Bank Transfer payments.", "monoova-payments-for-woocommerce")
}, /*#__PURE__*/React.createElement(StableTextareaControl, {
  value: settings.instructions || "",
  onChange: onChangeHandlers.instructions,
  rows: 4,
  disabled: !settings.enable_payid_payments
})), /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.PanelRow, null, /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.Flex, {
  align: "center",
  justify: "flex-start",
  gap: 3,
  style: {
    width: "100%"
  }
}, /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.CheckboxControl, {
  checked: settings.payid_show_reference_field,
  onChange: onChangeHandlers.payid_show_reference_field,
  disabled: !settings.enable_payid_payments
}), /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.__experimentalVStack, {
  spacing: 1
}, /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.__experimentalText, {
  weight: "500",
  size: "14"
}, (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("Display Payment Reference Field", "monoova-payments-for-woocommerce")), /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.__experimentalText, {
  variant: "muted",
  size: "13"
}, (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("If enabled, a separate 'Payment Reference' field will be shown below the QR code and in the bank transfer details.", "monoova-payments-for-woocommerce")))))))))), /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.__experimentalGrid, {
  columns: 12,
  gap: 6,
  className: "monoova-settings-section"
}, /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.__experimentalVStack, {
  spacing: 3,
  style: {
    gridColumn: "span 4"
  }
}, /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.__experimentalHeading, {
  level: 3
}, (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("Checkout UI Style Settings", "monoova-payments-for-woocommerce")), /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.__experimentalText, {
  variant: "muted",
  size: "14"
}, (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("Customize the appearance of the PayID payment display elements.", "monoova-payments-for-woocommerce"))), /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.__experimentalVStack, {
  spacing: 4,
  style: {
    gridColumn: "span 8"
  }
}, /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.Card, null, /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.CardBody, null, /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.__experimentalVStack, {
  spacing: 4
}, /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.Flex, {
  align: "center",
  justify: "flex-start"
}, /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.__experimentalText, {
  weight: "500",
  size: "15"
}, (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("Order Amount Display", "monoova-payments-for-woocommerce")), /*#__PURE__*/React.createElement(InfoIcon, {
  type: "scan_pay"
})), /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.__experimentalVStack, {
  spacing: 3
}, /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.__experimentalText, {
  weight: "500",
  size: "14"
}, (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("Pay Label", "monoova-payments-for-woocommerce")), /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.__experimentalGrid, {
  columns: 12,
  gap: 4,
  style: {
    gap: "16px"
  }
}, /*#__PURE__*/React.createElement("div", {
  style: {
    gridColumn: "span 7"
  }
}, /*#__PURE__*/React.createElement(FormField, {
  label: (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("Font size", "monoova-payments-for-woocommerce")
}, /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.SelectControl, {
  value: settings.payid_checkout_ui_styles?.scan_pay?.pay_label?.font_size || "28px",
  onChange: value => onChangeHandlers.payid_checkout_ui_styles({
    ...settings.payid_checkout_ui_styles,
    scan_pay: {
      ...settings.payid_checkout_ui_styles?.scan_pay,
      pay_label: {
        ...settings.payid_checkout_ui_styles?.scan_pay?.pay_label,
        font_size: value
      }
    }
  }),
  disabled: !settings.enable_payid_payments,
  options: [{
    label: "20px",
    value: "20px"
  }, {
    label: "24px",
    value: "24px"
  }, {
    label: "28px",
    value: "28px"
  }, {
    label: "32px",
    value: "32px"
  }, {
    label: "36px",
    value: "36px"
  }]
}))), /*#__PURE__*/React.createElement("div", {
  style: {
    gridColumn: "span 3"
  }
}, /*#__PURE__*/React.createElement(FormField, {
  label: (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("Font weight", "monoova-payments-for-woocommerce")
}, /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.SelectControl, {
  value: settings.payid_checkout_ui_styles?.scan_pay?.pay_label?.font_weight || "700",
  onChange: value => onChangeHandlers.payid_checkout_ui_styles({
    ...settings.payid_checkout_ui_styles,
    scan_pay: {
      ...settings.payid_checkout_ui_styles?.scan_pay,
      pay_label: {
        ...settings.payid_checkout_ui_styles?.scan_pay?.pay_label,
        font_weight: value
      }
    }
  }),
  disabled: !settings.enable_payid_payments,
  options: [{
    label: "Regular",
    value: "normal"
  }, {
    label: "Bold",
    value: "bold"
  }, {
    label: "Light",
    value: "300"
  }, {
    label: "Medium",
    value: "500"
  }, {
    label: "Semi Bold",
    value: "600"
  }, {
    label: "Extra Bold",
    value: "700"
  }]
}))), /*#__PURE__*/React.createElement("div", {
  style: {
    gridColumn: "span 2"
  }
}, /*#__PURE__*/React.createElement(ColorField, {
  label: (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("Color", "monoova-payments-for-woocommerce"),
  value: settings.payid_checkout_ui_styles?.scan_pay?.pay_label?.color || "#000000",
  onChange: value => onChangeHandlers.payid_checkout_ui_styles({
    ...settings.payid_checkout_ui_styles,
    scan_pay: {
      ...settings.payid_checkout_ui_styles?.scan_pay,
      pay_label: {
        ...settings.payid_checkout_ui_styles?.scan_pay?.pay_label,
        color: value
      }
    }
  }),
  disabled: !settings.enable_payid_payments
})))), /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.__experimentalVStack, {
  spacing: 3
}, /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.__experimentalText, {
  weight: "500",
  size: "14"
}, (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("Amount", "monoova-payments-for-woocommerce")), /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.__experimentalGrid, {
  columns: 12,
  gap: 4,
  style: {
    gap: "16px"
  }
}, /*#__PURE__*/React.createElement("div", {
  style: {
    gridColumn: "span 7"
  }
}, /*#__PURE__*/React.createElement(FormField, {
  label: (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("Font size", "monoova-payments-for-woocommerce")
}, /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.SelectControl, {
  value: settings.payid_checkout_ui_styles?.scan_pay?.amount?.font_size || "28px",
  onChange: value => onChangeHandlers.payid_checkout_ui_styles({
    ...settings.payid_checkout_ui_styles,
    scan_pay: {
      ...settings.payid_checkout_ui_styles?.scan_pay,
      amount: {
        ...settings.payid_checkout_ui_styles?.scan_pay?.amount,
        font_size: value
      }
    }
  }),
  disabled: !settings.enable_payid_payments,
  options: [{
    label: "20px",
    value: "20px"
  }, {
    label: "24px",
    value: "24px"
  }, {
    label: "28px",
    value: "28px"
  }, {
    label: "32px",
    value: "32px"
  }, {
    label: "36px",
    value: "36px"
  }]
}))), /*#__PURE__*/React.createElement("div", {
  style: {
    gridColumn: "span 3"
  }
}, /*#__PURE__*/React.createElement(FormField, {
  label: (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("Font weight", "monoova-payments-for-woocommerce")
}, /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.SelectControl, {
  value: settings.payid_checkout_ui_styles?.scan_pay?.amount?.font_weight || "700",
  onChange: value => onChangeHandlers.payid_checkout_ui_styles({
    ...settings.payid_checkout_ui_styles,
    scan_pay: {
      ...settings.payid_checkout_ui_styles?.scan_pay,
      amount: {
        ...settings.payid_checkout_ui_styles?.scan_pay?.amount,
        font_weight: value
      }
    }
  }),
  disabled: !settings.enable_payid_payments,
  options: [{
    label: "Regular",
    value: "normal"
  }, {
    label: "Bold",
    value: "bold"
  }, {
    label: "Light",
    value: "300"
  }, {
    label: "Medium",
    value: "500"
  }, {
    label: "Semi Bold",
    value: "600"
  }, {
    label: "Extra Bold",
    value: "700"
  }]
}))), /*#__PURE__*/React.createElement("div", {
  style: {
    gridColumn: "span 2"
  }
}, /*#__PURE__*/React.createElement(ColorField, {
  label: (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("Color", "monoova-payments-for-woocommerce"),
  value: settings.payid_checkout_ui_styles?.scan_pay?.amount?.color || "#2CB5C5",
  onChange: value => onChangeHandlers.payid_checkout_ui_styles({
    ...settings.payid_checkout_ui_styles,
    scan_pay: {
      ...settings.payid_checkout_ui_styles?.scan_pay,
      amount: {
        ...settings.payid_checkout_ui_styles?.scan_pay?.amount,
        color: value
      }
    }
  }),
  disabled: !settings.enable_payid_payments
})))))))))));
const PayToSettingsTab = (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_1__.memo)(({
  settings,
  saveNotice,
  onChangeHandlers,
  setSaveNotice,
  onGenerateAutomatcher,
  isGenerating
}) => /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.__experimentalVStack, {
  spacing: 6,
  className: "monoova-payto-settings-tab"
}, saveNotice && /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.Notice, {
  className: "monoova-save-notice",
  status: saveNotice.type,
  onRemove: () => setSaveNotice(null),
  isDismissible: true
}, saveNotice.message), !settings.enable_payto_payments && /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.Notice, {
  status: "warning",
  isDismissible: false
}, (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("PayTo payments are disabled. Enable them in the Payment Methods tab to configure these settings.", "monoova-payments-for-woocommerce")), settings.enable_payto_payments && settings.enable_payto_express_checkout && /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.Notice, {
  status: "info",
  isDismissible: false
}, (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("PayTo Express Checkout is enabled. Customers with saved PayTo agreements can complete purchases with a single click.", "monoova-payments-for-woocommerce")), settings.enable_payto_payments && !settings.enable_payto_express_checkout && /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.Notice, {
  status: "info",
  isDismissible: false
}, (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("Enable PayTo Express Checkout in the Payment Methods tab to allow customers with saved agreements to complete purchases faster.", "monoova-payments-for-woocommerce")), /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.__experimentalGrid, {
  columns: 12,
  gap: 6,
  className: "monoova-settings-section"
}, /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.__experimentalVStack, {
  spacing: 3,
  style: {
    gridColumn: "span 4"
  }
}, /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.__experimentalHeading, {
  level: 3
}, (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("Basic Information", "monoova-payments-for-woocommerce")), /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.__experimentalText, {
  variant: "muted",
  size: "14"
}, (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("Configure how this payment method appears to customers.", "monoova-payments-for-woocommerce"))), /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.__experimentalVStack, {
  spacing: 4,
  style: {
    gridColumn: "span 8"
  }
}, /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.Card, null, /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.CardBody, null, /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.__experimentalVStack, {
  spacing: 4
}, /*#__PURE__*/React.createElement(FormField, {
  label: (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("Title", "monoova-payments-for-woocommerce"),
  description: (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("This controls the title which the user sees during checkout.", "monoova-payments-for-woocommerce"),
  required: true
}, /*#__PURE__*/React.createElement(StableTextControl, {
  value: settings.payto_title || "",
  onChange: onChangeHandlers.payto_title,
  disabled: !settings.enable_payto_payments,
  placeholder: (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("Enter PayTo payment method title", "monoova-payments-for-woocommerce")
})), /*#__PURE__*/React.createElement(FormField, {
  label: (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("Description", "monoova-payments-for-woocommerce"),
  description: (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("This controls the description which the user sees during checkout.", "monoova-payments-for-woocommerce")
}, /*#__PURE__*/React.createElement(StableTextareaControl, {
  value: settings.payto_description || "",
  onChange: onChangeHandlers.payto_description,
  disabled: !settings.enable_payto_payments,
  rows: 3,
  placeholder: (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("Enter PayTo payment method description", "monoova-payments-for-woocommerce")
}))))))), /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.__experimentalGrid, {
  columns: 12,
  gap: 6,
  className: "monoova-settings-section"
}, /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.__experimentalVStack, {
  spacing: 3,
  style: {
    gridColumn: "span 4"
  }
}, /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.__experimentalHeading, {
  level: 3
}, (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("PayTo Settings", "monoova-payments-for-woocommerce")), /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.__experimentalText, {
  variant: "muted",
  size: "14"
}, (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("Configure PayTo payment gateway behavior and debugging options.", "monoova-payments-for-woocommerce"))), /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.__experimentalVStack, {
  spacing: 4,
  style: {
    gridColumn: "span 8"
  }
}, /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.Card, null, /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.CardBody, null, /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.__experimentalVStack, {
  spacing: 4
}, /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.PanelRow, null, /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.Flex, {
  align: "center",
  justify: "flex-start",
  gap: 3,
  style: {
    width: "100%"
  }
}, /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.CheckboxControl, {
  checked: settings.payto_testmode,
  onChange: onChangeHandlers.payto_testmode,
  disabled: !settings.enable_payto_payments
}), /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.__experimentalVStack, {
  spacing: 1
}, /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.__experimentalText, {
  weight: "500",
  size: "14"
}, (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("Enable Test Mode", "monoova-payments-for-woocommerce")), /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.__experimentalText, {
  variant: "muted",
  size: "13"
}, (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("Enable this to use sandbox/test environment for PayTo payments.", "monoova-payments-for-woocommerce"))))), /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.PanelRow, null, /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.Flex, {
  align: "center",
  justify: "flex-start",
  gap: 3,
  style: {
    width: "100%"
  }
}, /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.CheckboxControl, {
  checked: settings.payto_debug,
  onChange: onChangeHandlers.payto_debug,
  disabled: !settings.enable_payto_payments
}), /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.__experimentalVStack, {
  spacing: 1
}, /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.__experimentalText, {
  weight: "500",
  size: "14"
}, (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("Enable Debug Mode", "monoova-payments-for-woocommerce")), /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.__experimentalText, {
  variant: "muted",
  size: "13"
}, (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("Enable debug logging for PayTo transactions. Check logs under WooCommerce > Status > Logs.", "monoova-payments-for-woocommerce")))))))))), /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.__experimentalGrid, {
  columns: 12,
  gap: 6,
  className: "monoova-settings-section"
}, /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.__experimentalVStack, {
  spacing: 3,
  style: {
    gridColumn: "span 4"
  }
}, /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.__experimentalHeading, {
  level: 3
}, (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("Payment Agreement Settings", "monoova-payments-for-woocommerce")), /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.__experimentalText, {
  variant: "muted",
  size: "14"
}, (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("Configure PayTo payment agreement settings including purpose codes and expiry dates.", "monoova-payments-for-woocommerce"))), /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.__experimentalVStack, {
  spacing: 4,
  style: {
    gridColumn: "span 8"
  }
}, /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.Card, null, /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.CardBody, null, /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.__experimentalVStack, {
  spacing: 4
}, /*#__PURE__*/React.createElement(FormField, {
  label: (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("Purpose Code", "monoova-payments-for-woocommerce"),
  description: (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("Purpose code for PayTo agreements as per ISO 20022 standards. This indicates the purpose of the payment.", "monoova-payments-for-woocommerce")
}, /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.SelectControl, {
  value: settings.payto_purpose || "OTHR",
  onChange: onChangeHandlers.payto_purpose,
  disabled: !settings.enable_payto_payments,
  options: [{
    label: "MORT - Mortgage",
    value: "MORT"
  }, {
    label: "UTIL - Utilities",
    value: "UTIL"
  }, {
    label: "LOAN - Loan",
    value: "LOAN"
  }, {
    label: "DEPD - Deposit",
    value: "DEPD"
  }, {
    label: "GAMP - Gaming/Gambling",
    value: "GAMP"
  }, {
    label: "RETL - Retail",
    value: "RETL"
  }, {
    label: "SALA - Salary Payment",
    value: "SALA"
  }, {
    label: "PERS - Personal",
    value: "PERS"
  }, {
    label: "GOVT - Government",
    value: "GOVT"
  }, {
    label: "PENS - Pension",
    value: "PENS"
  }, {
    label: "TAXS - Tax Payment",
    value: "TAXS"
  }, {
    label: "OTHR - Other",
    value: "OTHR"
  }]
})), /*#__PURE__*/React.createElement(FormField, {
  label: (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("Agreement Expiry (Days)", "monoova-payments-for-woocommerce"),
  description: (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("Number of days from creation after which the PayTo agreement will expire. Leave empty for no expiry.", "monoova-payments-for-woocommerce")
}, /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.TextControl, {
  value: settings.payto_agreement_expiry_days || "",
  onChange: onChangeHandlers.payto_agreement_expiry_days,
  disabled: !settings.enable_payto_payments,
  type: "number",
  min: 1,
  max: 365,
  placeholder: (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("e.g. 30", "monoova-payments-for-woocommerce")
})), /*#__PURE__*/React.createElement(FormField, {
  label: (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("Payee Type", "monoova-payments-for-woocommerce"),
  description: (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("Type of payee for PayTo agreements. ORGN for organizations/businesses, PERS for individuals.", "monoova-payments-for-woocommerce")
}, /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.SelectControl, {
  value: settings.payto_payee_type || "ORGN",
  onChange: onChangeHandlers.payto_payee_type,
  disabled: !settings.enable_payto_payments,
  options: [{
    label: "ORGN - Organization",
    value: "ORGN"
  }, {
    label: "PERS - Person",
    value: "PERS"
  }]
})), /*#__PURE__*/React.createElement(FormField, {
  label: (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("Default Maximum Amount (AUD)", "monoova-payments-for-woocommerce"),
  description: (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("Default maximum amount for PayTo agreements. This can be modified by customers during checkout. Higher amounts provide more flexibility for variable pricing.", "monoova-payments-for-woocommerce")
}, /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.TextControl, {
  value: settings.payto_maximum_amount || "1000",
  onChange: onChangeHandlers.payto_maximum_amount,
  disabled: !settings.enable_payto_payments,
  type: "number",
  min: 0.01,
  step: 0.01,
  placeholder: (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("e.g. 1000", "monoova-payments-for-woocommerce")
}))))))), /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.__experimentalGrid, {
  columns: 12,
  gap: 6,
  className: "monoova-settings-section"
}, /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.__experimentalVStack, {
  spacing: 3,
  style: {
    gridColumn: "span 4"
  }
}, /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.__experimentalHeading, {
  level: 3
}, (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("Unified Automatcher Account", "monoova-payments-for-woocommerce")), /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.__experimentalText, {
  variant: "muted",
  size: "14"
}, (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("PayTo requires the same unified automatcher account as PayID for payment processing. This account is shared between PayID and PayTo payment methods.", "monoova-payments-for-woocommerce"))), /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.__experimentalVStack, {
  spacing: 4,
  style: {
    gridColumn: "span 8"
  }
}, /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.Card, null, /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.CardBody, null, /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.__experimentalVStack, {
  spacing: 4
}, /*#__PURE__*/React.createElement(FormField, {
  label: (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("Account Name", "monoova-payments-for-woocommerce"),
  description: (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("The account name for the unified Automatcher account used by both PayID and PayTo.", "monoova-payments-for-woocommerce"),
  required: true
}, /*#__PURE__*/React.createElement(StableTextControl, {
  value: settings.account_name || "",
  onChange: onChangeHandlers.account_name,
  disabled: !settings.enable_payto_payments,
  placeholder: (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("e.g. Your Store Name", "monoova-payments-for-woocommerce")
})), /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.__experimentalGrid, {
  columns: 2,
  gap: 4
}, /*#__PURE__*/React.createElement(FormField, {
  label: (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("Store BSB", "monoova-payments-for-woocommerce")
}, /*#__PURE__*/React.createElement(StableTextControl, {
  value: settings.static_bsb || "",
  readOnly: true,
  disabled: !settings.enable_payto_payments,
  placeholder: (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("Generated by Monoova", "monoova-payments-for-woocommerce")
})), /*#__PURE__*/React.createElement(FormField, {
  label: (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("Store Account Number", "monoova-payments-for-woocommerce")
}, /*#__PURE__*/React.createElement(StableTextControl, {
  value: settings.static_account_number || "",
  readOnly: true,
  disabled: !settings.enable_payto_payments,
  placeholder: (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("Generated by Monoova", "monoova-payments-for-woocommerce")
}))), /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.Button, {
  variant: "secondary",
  onClick: onGenerateAutomatcher,
  isBusy: isGenerating,
  disabled: !settings.enable_payto_payments || isGenerating
}, (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("Generate / Replace Store Automatcher Account", "monoova-payments-for-woocommerce")), /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.__experimentalText, {
  variant: "muted",
  size: "13"
}, (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("Note: This is the same automatcher account used by PayID. Changes here will affect both PayID and PayTo payment methods. It may take up to 5 minutes for a newly generated account to become fully active.", "monoova-payments-for-woocommerce"))))))), /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.__experimentalGrid, {
  columns: 12,
  gap: 6,
  className: "monoova-settings-section"
}, /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.__experimentalVStack, {
  spacing: 3,
  style: {
    gridColumn: "span 4"
  }
}, /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.__experimentalHeading, {
  level: 3
}, (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("Checkout UI Style Settings", "monoova-payments-for-woocommerce")), /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.__experimentalText, {
  variant: "muted",
  size: "14"
}, (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("Customize the appearance of the PayTo payment display elements and buttons.", "monoova-payments-for-woocommerce"))), /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.__experimentalVStack, {
  spacing: 4,
  style: {
    gridColumn: "span 8"
  }
}, /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.Card, null, /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.CardBody, null, /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.__experimentalVStack, {
  spacing: 4
}, /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.Flex, {
  align: "center",
  justify: "flex-start"
}, /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.__experimentalText, {
  weight: "500",
  size: "15"
}, (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("Order Amount Display", "monoova-payments-for-woocommerce")), /*#__PURE__*/React.createElement(InfoIcon, {
  type: "scan_pay"
})), /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.__experimentalVStack, {
  spacing: 3
}, /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.__experimentalText, {
  weight: "500",
  size: "14"
}, (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("Pay Label", "monoova-payments-for-woocommerce")), /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.__experimentalGrid, {
  columns: 12,
  gap: 4,
  style: {
    gap: "16px"
  }
}, /*#__PURE__*/React.createElement("div", {
  style: {
    gridColumn: "span 7"
  }
}, /*#__PURE__*/React.createElement(FormField, {
  label: (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("Font size", "monoova-payments-for-woocommerce")
}, /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.SelectControl, {
  value: settings.payto_checkout_ui_styles?.scan_pay?.pay_label?.font_size || "28px",
  onChange: value => onChangeHandlers.payto_checkout_ui_styles({
    ...settings.payto_checkout_ui_styles,
    scan_pay: {
      ...settings.payto_checkout_ui_styles?.scan_pay,
      pay_label: {
        ...settings.payto_checkout_ui_styles?.scan_pay?.pay_label,
        font_size: value
      }
    }
  }),
  disabled: !settings.enable_payto_payments,
  options: [{
    label: "20px",
    value: "20px"
  }, {
    label: "24px",
    value: "24px"
  }, {
    label: "28px",
    value: "28px"
  }, {
    label: "32px",
    value: "32px"
  }, {
    label: "36px",
    value: "36px"
  }]
}))), /*#__PURE__*/React.createElement("div", {
  style: {
    gridColumn: "span 3"
  }
}, /*#__PURE__*/React.createElement(FormField, {
  label: (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("Font weight", "monoova-payments-for-woocommerce")
}, /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.SelectControl, {
  value: settings.payto_checkout_ui_styles?.scan_pay?.pay_label?.font_weight || "700",
  onChange: value => onChangeHandlers.payto_checkout_ui_styles({
    ...settings.payto_checkout_ui_styles,
    scan_pay: {
      ...settings.payto_checkout_ui_styles?.scan_pay,
      pay_label: {
        ...settings.payto_checkout_ui_styles?.scan_pay?.pay_label,
        font_weight: value
      }
    }
  }),
  disabled: !settings.enable_payto_payments,
  options: [{
    label: "Regular",
    value: "normal"
  }, {
    label: "Bold",
    value: "bold"
  }, {
    label: "Light",
    value: "300"
  }, {
    label: "Medium",
    value: "500"
  }, {
    label: "Semi Bold",
    value: "600"
  }, {
    label: "Extra Bold",
    value: "700"
  }]
}))), /*#__PURE__*/React.createElement("div", {
  style: {
    gridColumn: "span 2"
  }
}, /*#__PURE__*/React.createElement(ColorField, {
  label: (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("Color", "monoova-payments-for-woocommerce"),
  value: settings.payto_checkout_ui_styles?.scan_pay?.pay_label?.color || "#000000",
  onChange: value => onChangeHandlers.payto_checkout_ui_styles({
    ...settings.payto_checkout_ui_styles,
    scan_pay: {
      ...settings.payto_checkout_ui_styles?.scan_pay,
      pay_label: {
        ...settings.payto_checkout_ui_styles?.scan_pay?.pay_label,
        color: value
      }
    }
  }),
  disabled: !settings.enable_payto_payments
})))), /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.__experimentalVStack, {
  spacing: 3
}, /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.__experimentalText, {
  weight: "500",
  size: "14"
}, (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("Amount", "monoova-payments-for-woocommerce")), /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.__experimentalGrid, {
  columns: 12,
  gap: 4,
  style: {
    gap: "16px"
  }
}, /*#__PURE__*/React.createElement("div", {
  style: {
    gridColumn: "span 7"
  }
}, /*#__PURE__*/React.createElement(FormField, {
  label: (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("Font size", "monoova-payments-for-woocommerce")
}, /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.SelectControl, {
  value: settings.payto_checkout_ui_styles?.scan_pay?.amount?.font_size || "28px",
  onChange: value => onChangeHandlers.payto_checkout_ui_styles({
    ...settings.payto_checkout_ui_styles,
    scan_pay: {
      ...settings.payto_checkout_ui_styles?.scan_pay,
      amount: {
        ...settings.payto_checkout_ui_styles?.scan_pay?.amount,
        font_size: value
      }
    }
  }),
  disabled: !settings.enable_payto_payments,
  options: [{
    label: "20px",
    value: "20px"
  }, {
    label: "24px",
    value: "24px"
  }, {
    label: "28px",
    value: "28px"
  }, {
    label: "32px",
    value: "32px"
  }, {
    label: "36px",
    value: "36px"
  }]
}))), /*#__PURE__*/React.createElement("div", {
  style: {
    gridColumn: "span 3"
  }
}, /*#__PURE__*/React.createElement(FormField, {
  label: (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("Font weight", "monoova-payments-for-woocommerce")
}, /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.SelectControl, {
  value: settings.payto_checkout_ui_styles?.scan_pay?.amount?.font_weight || "700",
  onChange: value => onChangeHandlers.payto_checkout_ui_styles({
    ...settings.payto_checkout_ui_styles,
    scan_pay: {
      ...settings.payto_checkout_ui_styles?.scan_pay,
      amount: {
        ...settings.payto_checkout_ui_styles?.scan_pay?.amount,
        font_weight: value
      }
    }
  }),
  disabled: !settings.enable_payto_payments,
  options: [{
    label: "Regular",
    value: "normal"
  }, {
    label: "Bold",
    value: "bold"
  }, {
    label: "Light",
    value: "300"
  }, {
    label: "Medium",
    value: "500"
  }, {
    label: "Semi Bold",
    value: "600"
  }, {
    label: "Extra Bold",
    value: "700"
  }]
}))), /*#__PURE__*/React.createElement("div", {
  style: {
    gridColumn: "span 2"
  }
}, /*#__PURE__*/React.createElement(ColorField, {
  label: (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("Color", "monoova-payments-for-woocommerce"),
  value: settings.payto_checkout_ui_styles?.scan_pay?.amount?.color || "#2CB5C5",
  onChange: value => onChangeHandlers.payto_checkout_ui_styles({
    ...settings.payto_checkout_ui_styles,
    scan_pay: {
      ...settings.payto_checkout_ui_styles?.scan_pay,
      amount: {
        ...settings.payto_checkout_ui_styles?.scan_pay?.amount,
        color: value
      }
    }
  }),
  disabled: !settings.enable_payto_payments
}))))))), /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.Card, null, /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.CardBody, null, /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.__experimentalVStack, {
  spacing: 4
}, /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.Flex, {
  align: "center",
  justify: "flex-start"
}, /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.__experimentalText, {
  weight: "500",
  size: "15"
}, (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("Pay Button", "monoova-payments-for-woocommerce")), /*#__PURE__*/React.createElement(InfoIcon, {
  type: "button"
})), /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.__experimentalGrid, {
  columns: 12,
  gap: 4,
  style: {
    gap: "16px"
  }
}, /*#__PURE__*/React.createElement("div", {
  style: {
    gridColumn: "span 7"
  }
}, /*#__PURE__*/React.createElement(FormField, {
  label: (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("Font family", "monoova-payments-for-woocommerce")
}, /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.SelectControl, {
  value: settings.payto_checkout_ui_styles?.submit_button?.font_family || "Helvetica, Arial, sans-serif",
  onChange: value => onChangeHandlers.payto_checkout_ui_styles({
    ...settings.payto_checkout_ui_styles,
    submit_button: {
      ...settings.payto_checkout_ui_styles?.submit_button,
      font_family: value
    }
  }),
  disabled: !settings.enable_payto_payments,
  options: [{
    label: "Inter",
    value: "Inter"
  }, {
    label: "Helvetica",
    value: "Helvetica, Arial, sans-serif"
  }, {
    label: "Arial",
    value: "Arial, sans-serif"
  }, {
    label: "Times New Roman",
    value: "Times New Roman, serif"
  }, {
    label: "Courier New",
    value: "Courier New, monospace"
  }]
}))), /*#__PURE__*/React.createElement("div", {
  style: {
    gridColumn: "span 3"
  }
}, /*#__PURE__*/React.createElement(FormField, {
  label: (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("Font weight", "monoova-payments-for-woocommerce")
}, /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.SelectControl, {
  value: settings.payto_checkout_ui_styles?.submit_button?.font_weight || "bold",
  onChange: value => onChangeHandlers.payto_checkout_ui_styles({
    ...settings.payto_checkout_ui_styles,
    submit_button: {
      ...settings.payto_checkout_ui_styles?.submit_button,
      font_weight: value
    }
  }),
  disabled: !settings.enable_payto_payments,
  options: [{
    label: "Regular",
    value: "normal"
  }, {
    label: "Bold",
    value: "bold"
  }, {
    label: "Light",
    value: "300"
  }, {
    label: "Medium",
    value: "500"
  }, {
    label: "Semi Bold",
    value: "600"
  }]
}))), /*#__PURE__*/React.createElement("div", {
  style: {
    gridColumn: "span 2"
  }
}, /*#__PURE__*/React.createElement(FormField, {
  label: (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("Font size", "monoova-payments-for-woocommerce")
}, /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.SelectControl, {
  value: settings.payto_checkout_ui_styles?.submit_button?.font_size || "17px",
  onChange: value => onChangeHandlers.payto_checkout_ui_styles({
    ...settings.payto_checkout_ui_styles,
    submit_button: {
      ...settings.payto_checkout_ui_styles?.submit_button,
      font_size: value
    }
  }),
  disabled: !settings.enable_payto_payments,
  options: [{
    label: "14px",
    value: "14px"
  }, {
    label: "16px",
    value: "16px"
  }, {
    label: "17px",
    value: "17px"
  }, {
    label: "18px",
    value: "18px"
  }, {
    label: "20px",
    value: "20px"
  }]
})))), /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.__experimentalGrid, {
  columns: 12,
  gap: 4,
  style: {
    gap: "16px"
  }
}, /*#__PURE__*/React.createElement("div", {
  style: {
    gridColumn: "span 3"
  }
}, /*#__PURE__*/React.createElement(ColorField, {
  label: (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("Background color", "monoova-payments-for-woocommerce"),
  value: settings.payto_checkout_ui_styles?.submit_button?.background || "#2ab5c4",
  onChange: value => onChangeHandlers.payto_checkout_ui_styles({
    ...settings.payto_checkout_ui_styles,
    submit_button: {
      ...settings.payto_checkout_ui_styles?.submit_button,
      background: value
    }
  }),
  disabled: !settings.enable_payto_payments
})), /*#__PURE__*/React.createElement("div", {
  style: {
    gridColumn: "span 3"
  }
}, /*#__PURE__*/React.createElement(ColorField, {
  label: (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("Border color", "monoova-payments-for-woocommerce"),
  value: settings.payto_checkout_ui_styles?.submit_button?.border_color || "#2ab5c4",
  onChange: value => onChangeHandlers.payto_checkout_ui_styles({
    ...settings.payto_checkout_ui_styles,
    submit_button: {
      ...settings.payto_checkout_ui_styles?.submit_button,
      border_color: value
    }
  }),
  disabled: !settings.enable_payto_payments
})), /*#__PURE__*/React.createElement("div", {
  style: {
    gridColumn: "span 3"
  }
}, /*#__PURE__*/React.createElement(ColorField, {
  label: (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("Text color", "monoova-payments-for-woocommerce"),
  value: settings.payto_checkout_ui_styles?.submit_button?.text_color || "#000000",
  onChange: value => onChangeHandlers.payto_checkout_ui_styles({
    ...settings.payto_checkout_ui_styles,
    submit_button: {
      ...settings.payto_checkout_ui_styles?.submit_button,
      text_color: value
    }
  }),
  disabled: !settings.enable_payto_payments
})), /*#__PURE__*/React.createElement("div", {
  style: {
    gridColumn: "span 3"
  }
}, /*#__PURE__*/React.createElement(FormField, {
  label: (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("Border radius", "monoova-payments-for-woocommerce")
}, /*#__PURE__*/React.createElement(StableTextControl, {
  value: settings.payto_checkout_ui_styles?.submit_button?.border_radius || "10px",
  onChange: value => onChangeHandlers.payto_checkout_ui_styles({
    ...settings.payto_checkout_ui_styles,
    submit_button: {
      ...settings.payto_checkout_ui_styles?.submit_button,
      border_radius: value
    }
  }),
  disabled: !settings.enable_payto_payments,
  style: {
    borderRadius: "8px",
    padding: "10px",
    borderColor: "#D0D5DD"
  },
  placeholder: "10px"
})))))))))));
const PaymentSettings = () => {
  const [settings, setSettings] = (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_1__.useState)({
    // Payment Methods Tab
    enable_card_payments: false,
    enable_payid_payments: false,
    enable_payto_payments: false,
    enable_express_checkout: false,
    enable_payto_express_checkout: false,
    express_checkout_method_priority: "card",
    // Card Settings Tab
    card_title: "Credit / Debit Card",
    card_description: "Pay with your credit or debit card via Monoova.",
    card_testmode: true,
    card_debug: true,
    capture: true,
    saved_cards: true,
    apply_surcharge: false,
    surcharge_amount: 0.0,
    enable_apple_pay: true,
    enable_google_pay: true,
    order_button_text: "Pay with Card",
    // Checkout UI Style Settings
    checkout_ui_styles: {
      input_label: {
        font_family: "Helvetica, Arial, sans-serif",
        font_weight: "normal",
        font_size: "14px",
        color: "#000000"
      },
      input: {
        font_family: "Helvetica, Arial, sans-serif",
        font_weight: "normal",
        font_size: "14px",
        background_color: "#FAFAFA",
        border_color: "#E8E8E8",
        border_radius: "8px",
        text_color: "#000000"
      },
      submit_button: {
        font_family: "Helvetica, Arial, sans-serif",
        font_size: "17px",
        background: "#2ab5c4",
        border_radius: "10px",
        border_color: "#2ab5c4",
        font_weight: "bold",
        text_color: "#000000"
      }
    },
    // PayID Settings Tab
    payid_title: "PayID / Bank Transfer",
    payid_description: "Pay using PayID or bank transfer.",
    payid_testmode: true,
    payid_debug: true,
    payid_show_reference_field: true,
    static_bank_account_name: "",
    static_bsb: "",
    static_account_number: "",
    payment_types: ["payid", "bank_transfer"],
    expire_hours: 24,
    account_name: "",
    instructions: "",
    // PayID Checkout UI Style Settings
    payid_checkout_ui_styles: {
      scan_pay: {
        pay_label: {
          font_size: "28px",
          font_weight: "700",
          color: "#000000"
        },
        amount: {
          font_size: "28px",
          font_weight: "700",
          color: "#2CB5C5"
        }
      }
    },
    // PayTo Settings Tab
    payto_title: "PayTo",
    payto_description: "Approve a payment agreement to complete the purchase.",
    payto_testmode: true,
    payto_debug: true,
    payto_purpose: "OTHR",
    payto_agreement_expiry_days: "",
    // PayTo Checkout UI Style Settings
    payto_checkout_ui_styles: {
      scan_pay: {
        pay_label: {
          font_size: "28px",
          font_weight: "700",
          color: "#000000"
        },
        amount: {
          font_size: "28px",
          font_weight: "700",
          color: "#2CB5C5"
        }
      },
      submit_button: {
        font_family: "Helvetica, Arial, sans-serif",
        font_size: "17px",
        background: "#2ab5c4",
        border_radius: "10px",
        border_color: "#2ab5c4",
        font_weight: "bold",
        text_color: "#000000"
      }
    },
    payto_payee_type: "ORGN",
    payto_maximum_amount: 1000,
    // General settings from parent gateway
    enabled: true,
    maccount_number: "",
    test_api_key: "",
    live_api_key: "",
    monoova_payments_api_url_sandbox: "https://api.m-pay.com.au",
    monoova_payments_api_url_live: "https://api.mpay.com.au",
    monoova_card_api_url_sandbox: "https://sand-api.monoova.com",
    monoova_card_api_url_live: "https://api.monoova.com"
  });
  const [isSaving, setIsSaving] = (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_1__.useState)(false);
  const [saveNotice, setSaveNotice] = (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_1__.useState)(null);
  const [validationErrors, setValidationErrors] = (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_1__.useState)({});
  const [isGenerating, setIsGenerating] = (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_1__.useState)(false);

  // Webhook status state for both sandbox and live modes
  const [webhookStatus, setWebhookStatus] = (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_1__.useState)({
    sandbox: {
      all_active: false,
      card_active: false,
      payto_active: false,
      payments_active: false,
      isChecking: false,
      isConnecting: false,
      lastChecked: null
    },
    live: {
      all_active: false,
      card_active: false,
      payto_active: false,
      payments_active: false,
      isChecking: false,
      isConnecting: false,
      lastChecked: null
    }
  });

  // Utility function to scroll to notice
  const scrollToNotice = (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_1__.useCallback)(() => {
    setTimeout(() => {
      const noticeElement = document.querySelector(".monoova-save-notice");
      if (noticeElement) {
        noticeElement.scrollIntoView({
          behavior: "smooth",
          block: "center"
        });
      }
    }, 100);
  }, []);

  // Validation function to check if API credentials are available for a mode
  const validateApiCredentials = isTestmode => {
    const mode = isTestmode ? "sandbox" : "live";
    const apiKey = isTestmode ? settings.test_api_key : settings.live_api_key;
    const paymentsApiUrl = isTestmode ? settings.monoova_payments_api_url_sandbox : settings.monoova_payments_api_url_live;
    const cardApiUrl = isTestmode ? settings.monoova_card_api_url_sandbox : settings.monoova_card_api_url_live;
    const maccountNumber = settings.maccount_number;
    const missingFields = [];
    if (!apiKey || apiKey.trim() === "") {
      missingFields.push("API Key");
    }
    if (!paymentsApiUrl || paymentsApiUrl.trim() === "") {
      missingFields.push("PayID API URL");
    }
    if (!cardApiUrl || cardApiUrl.trim() === "") {
      missingFields.push("Card API URL");
    }
    if (!maccountNumber || maccountNumber.trim() === "") {
      missingFields.push("mAccount Number");
    }
    return {
      isValid: missingFields.length === 0,
      missingFields,
      mode: mode.charAt(0).toUpperCase() + mode.slice(1)
    };
  };

  // Load settings on component mount
  (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_1__.useEffect)(() => {
    const loadSettings = async () => {
      if (window.monoovaAdminSettings) {
        const processedSettings = {
          ...window.monoovaAdminSettings
        };

        // List of known boolean fields that need conversion
        const booleanFields = ["enabled", "enable_card_payments", "enable_payid_payments", "enable_payto_payments", "enable_express_checkout", "enable_payto_express_checkout", "capture", "saved_cards", "apply_surcharge", "enable_apple_pay", "enable_google_pay", "card_testmode", "card_debug", "payid_testmode", "payid_debug", "payid_show_reference_field", "payto_testmode", "payto_debug"];

        // Convert various boolean formats to actual booleans
        booleanFields.forEach(field => {
          const value = processedSettings[field];
          if (typeof value === "string") {
            // Handle 'yes'/'no', '1'/'0', and empty strings
            processedSettings[field] = value === "yes" || value === "1" || value === "true";
          } else if (typeof value === "number") {
            // Handle numeric 1/0
            processedSettings[field] = Boolean(value);
          } else if (typeof value === "boolean") {
            // Already boolean, no conversion needed
            processedSettings[field] = value;
          } else {
            // Default to false for any other type (null, undefined, etc.)
            processedSettings[field] = false;
          }
        });
        setSettings(prevSettings => ({
          ...prevSettings,
          ...processedSettings
        }));
      }
    };
    loadSettings();
  }, []);

  // Webhook status check function
  const checkWebhookStatus = async (isTestmode = true) => {
    const mode = isTestmode ? "sandbox" : "live";

    // Validate API credentials first
    const validation = validateApiCredentials(isTestmode);
    if (!validation.isValid) {
      setWebhookStatus(prev => ({
        ...prev,
        [mode]: {
          ...prev[mode],
          isChecking: false,
          all_active: false,
          card_active: false,
          payto_active: false,
          payments_active: false,
          validationError: `Monoova API client (${validation.mode}) is not available. Please check your configuration on API Credentials and API URLs sections.`
        }
      }));
      return;
    }
    setWebhookStatus(prev => ({
      ...prev,
      [mode]: {
        ...prev[mode],
        isChecking: true,
        validationError: null
      }
    }));
    try {
      if (!window.monoovaCheckWebhookSubscriptionsNonce) {
        throw new Error("Security nonce for checking webhook subscriptions not available. Please refresh the page.");
      }
      const formData = new FormData();
      formData.append("action", "monoova_check_webhook_subscriptions_status");
      formData.append("nonce", window.monoovaCheckWebhookSubscriptionsNonce);
      formData.append("is_testmode", isTestmode.toString());
      const response = await fetch(window.ajaxUrl, {
        method: "POST",
        body: formData
      });
      const result = await response.json();
      if (result.success) {
        setWebhookStatus(prev => ({
          ...prev,
          [mode]: {
            ...prev[mode],
            all_active: result.data.all_active,
            card_active: result.data.card_active,
            payto_active: result.data.payto_active,
            payments_active: result.data.payments_active,
            lastChecked: new Date(),
            isChecking: false
          }
        }));
      } else {
        throw new Error(result.data?.message || "Failed to check webhook status.");
      }
    } catch (error) {
      console.error("Error checking webhook status:", error);
      setWebhookStatus(prev => ({
        ...prev,
        [mode]: {
          ...prev[mode],
          isChecking: false
        }
      }));
    }
  };

  // Re-check webhook status when API credentials change
  (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_1__.useEffect)(() => {
    // Only re-check if we have some settings loaded (not initial empty state)
    if (settings.maccount_number || settings.test_api_key || settings.live_api_key) {
      checkWebhookStatus(true); // Re-check sandbox
      checkWebhookStatus(false); // Re-check live
    }
  }, [settings.test_api_key, settings.live_api_key, settings.maccount_number, settings.monoova_payments_api_url_sandbox, settings.monoova_payments_api_url_live, settings.monoova_card_api_url_sandbox, settings.monoova_card_api_url_live]);
  const saveSettings = (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async (tabName = "all") => {
    setIsSaving(true);
    setSaveNotice(null);
    setValidationErrors({});
    try {
      // Validate live environment requirements
      const errors = {};

      // Check each payment method's live mode status independently
      const isCardLive = settings.enable_card_payments && !settings.card_testmode;
      const isPayidLive = settings.enable_payid_payments && !settings.payid_testmode;
      const isAnyLive = isCardLive || isPayidLive;
      if (isAnyLive) {
        // Live API Key is always required when any payment method is in live mode
        if (!settings.live_api_key || settings.live_api_key.trim() === "") {
          errors.live_api_key = (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("Live API Key is required when any payment method has test mode disabled.", "monoova-payments-for-woocommerce");
        }
      }

      // Validate Payments API URL if PayID is in live mode
      if (isPayidLive) {
        if (!settings.monoova_payments_api_url_live || settings.monoova_payments_api_url_live.trim() === "") {
          errors.monoova_payments_api_url_live = (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("Live Payments API URL is required when PayID test mode is disabled.", "monoova-payments-for-woocommerce");
        }
      }

      // Validate Card API URL if Card is in live mode
      if (isCardLive) {
        if (!settings.monoova_card_api_url_live || settings.monoova_card_api_url_live.trim() === "") {
          errors.monoova_card_api_url_live = (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("Live Card API URL is required when Card test mode is disabled.", "monoova-payments-for-woocommerce");
        }
      }

      // If there are validation errors, show them and stop the save process
      if (Object.keys(errors).length > 0) {
        setValidationErrors(errors);

        // Create a user-friendly error message with field labels
        const fieldLabels = {
          live_api_key: (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("Live API Key", "monoova-payments-for-woocommerce"),
          monoova_payments_api_url_live: (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("Live Payments API URL", "monoova-payments-for-woocommerce"),
          monoova_card_api_url_live: (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("Live Card API URL", "monoova-payments-for-woocommerce")
        };
        const errorFields = Object.keys(errors).map(field => fieldLabels[field] || field).join(", ");
        const errorMessage = (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("Please fix the following validation errors before saving: ", "monoova-payments-for-woocommerce") + errorFields;
        setSaveNotice({
          type: "error",
          message: errorMessage
        });
        setIsSaving(false);

        // Scroll to the notice
        scrollToNotice();
        return;
      }

      // Ensure nonce is available
      // if (!window.monoovaAdminNonce) {
      //     throw new Error("Security nonce not available")
      // }

      // Ensure boolean values are explicitly set as booleans
      const preparedSettings = {
        ...settings
      };

      // List of known boolean fields
      const booleanFields = ["enabled", "enable_card_payments", "enable_payid_payments", "enable_payto_payments", "enable_express_checkout", "enable_payto_express_checkout", "capture", "saved_cards", "apply_surcharge", "enable_apple_pay", "enable_google_pay", "card_testmode", "card_debug", "payid_testmode", "payid_debug", "payid_show_reference_field", "payto_testmode", "payto_debug"];
      booleanFields.forEach(field => {
        preparedSettings[field] = Boolean(preparedSettings[field]);
      });
      const formData = new FormData();
      formData.append("action", "monoova_save_payment_settings");
      // formData.append("nonce", window.monoovaAdminNonce)
      formData.append("settings", JSON.stringify(preparedSettings));
      formData.append("tab", tabName);
      const response = await fetch(window.ajaxUrl, {
        method: "POST",
        body: formData
      });
      const result = await response.json();
      if (result.success) {
        setSaveNotice({
          type: "success",
          message: result.data.message || (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("Settings saved successfully!", "monoova-payments-for-woocommerce")
        });

        // Update local settings with any changes from server
        if (result.data.settings) {
          setSettings(prevSettings => ({
            ...prevSettings,
            ...result.data.settings
          }));
        }

        // Scroll to the success notice
        scrollToNotice();
      } else {
        throw new Error(result.data?.message || "Unknown error occurred");
      }
    } catch (error) {
      console.error("Error saving settings:", error);
      setSaveNotice({
        type: "error",
        message: error.message || (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("Failed to save settings. Please try again.", "monoova-payments-for-woocommerce")
      });

      // Scroll to the error notice
      scrollToNotice();
    } finally {
      setIsSaving(false);
    }
  }, [settings, scrollToNotice]);

  // NEW: Handler for generating the Automatcher account
  const handleGenerateAutomatcher = (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async () => {
    setIsGenerating(true);
    setSaveNotice(null);
    try {
      if (!window.monoovaGenerateAutomatcherNonce) {
        throw new Error("Security nonce for generating automatcher not available. Please refresh the page.");
      }
      const formData = new FormData();
      formData.append("action", "monoova_generate_automatcher");
      formData.append("nonce", window.monoovaGenerateAutomatcherNonce);
      const response = await fetch(window.ajaxUrl, {
        method: "POST",
        body: formData
      });
      const result = await response.json();
      if (result.success) {
        setSaveNotice({
          type: "success",
          message: result.data.message || (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("Automatcher account generated successfully!", "monoova-payments-for-woocommerce")
        });
        // Update settings state with new values to refresh the UI
        setSettings(prevSettings => ({
          ...prevSettings,
          static_bsb: result.data.bsb,
          static_account_number: result.data.accountNumber,
          static_bank_account_name: result.data.accountName
        }));

        // Scroll to the success notice
        scrollToNotice();
      } else {
        throw new Error(result.data?.message || "Unknown error occurred while generating account.");
      }
    } catch (error) {
      console.error("Error generating Automatcher account:", error);
      setSaveNotice({
        type: "error",
        message: error.message || (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("Failed to generate Automatcher account. Please check logs.", "monoova-payments-for-woocommerce")
      });

      // Scroll to the error notice
      scrollToNotice();
    } finally {
      setIsGenerating(false);
    }
  }, []);

  // Webhook subscription function
  const subscribeToWebhooks = (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_1__.useCallback)(async (isTestmode = true) => {
    const mode = isTestmode ? "sandbox" : "live";

    // Validate API credentials first
    const validation = validateApiCredentials(isTestmode);
    if (!validation.isValid) {
      setSaveNotice({
        type: "error",
        message: `Monoova API client (${validation.mode}) is not available. Please check your configuration on API Credentials and API URLs sections.`
      });
      scrollToNotice();
      return;
    }
    setWebhookStatus(prev => ({
      ...prev,
      [mode]: {
        ...prev[mode],
        isConnecting: true,
        validationError: null
      }
    }));
    setSaveNotice(null);
    try {
      if (!window.monoovaSubscribeWebhookEventsNonce) {
        throw new Error("Security nonce for subscribing to webhook events not available. Please refresh the page.");
      }
      const formData = new FormData();
      formData.append("action", "monoova_subscribe_to_webhook_events");
      formData.append("nonce", window.monoovaSubscribeWebhookEventsNonce);
      formData.append("is_testmode", isTestmode.toString());
      const response = await fetch(window.ajaxUrl, {
        method: "POST",
        body: formData
      });
      const result = await response.json();
      if (result.success) {
        setSaveNotice({
          type: "success",
          message: result.data.message || (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("Webhook subscriptions updated successfully!", "monoova-payments-for-woocommerce")
        });

        // Check webhook status again after successful subscription
        await checkWebhookStatus(isTestmode);
        scrollToNotice();
      } else {
        throw new Error(result.data?.message || "Failed to subscribe to webhooks.");
      }
    } catch (error) {
      console.error("Error subscribing to webhooks:", error);
      setSaveNotice({
        type: "error",
        message: error.message || (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("Failed to subscribe to webhooks. Please check logs.", "monoova-payments-for-woocommerce")
      });
      scrollToNotice();
    } finally {
      setWebhookStatus(prev => ({
        ...prev,
        [mode]: {
          ...prev[mode],
          isConnecting: false
        }
      }));
    }
  }, [checkWebhookStatus, scrollToNotice]);
  const handleSettingChange = (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_1__.useCallback)((key, value) => {
    setSettings(prevSettings => ({
      ...prevSettings,
      [key]: value
    }));
  }, []);

  // Create stable onChange handlers using useMemo to prevent recreation on every render
  const onChangeHandlers = (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_1__.useMemo)(() => ({
    enabled: value => handleSettingChange("enabled", value),
    maccount_number: value => handleSettingChange("maccount_number", value),
    test_api_key: value => handleSettingChange("test_api_key", value),
    live_api_key: value => handleSettingChange("live_api_key", value),
    monoova_payments_api_url_sandbox: value => handleSettingChange("monoova_payments_api_url_sandbox", value),
    monoova_payments_api_url_live: value => handleSettingChange("monoova_payments_api_url_live", value),
    monoova_card_api_url_sandbox: value => handleSettingChange("monoova_card_api_url_sandbox", value),
    monoova_card_api_url_live: value => handleSettingChange("monoova_card_api_url_live", value),
    enable_card_payments: value => handleSettingChange("enable_card_payments", value),
    enable_payid_payments: value => handleSettingChange("enable_payid_payments", value),
    enable_payto_payments: value => handleSettingChange("enable_payto_payments", value),
    enable_express_checkout: value => handleSettingChange("enable_express_checkout", value),
    enable_payto_express_checkout: value => handleSettingChange("enable_payto_express_checkout", value),
    express_checkout_method_priority: value => handleSettingChange("express_checkout_method_priority", value),
    // Card-specific fields
    card_title: value => handleSettingChange("card_title", value),
    card_description: value => handleSettingChange("card_description", value),
    card_testmode: value => handleSettingChange("card_testmode", value),
    card_debug: value => handleSettingChange("card_debug", value),
    capture: value => handleSettingChange("capture", value),
    saved_cards: value => handleSettingChange("saved_cards", value),
    apply_surcharge: value => handleSettingChange("apply_surcharge", value),
    surcharge_amount: value => handleSettingChange("surcharge_amount", value),
    enable_apple_pay: value => handleSettingChange("enable_apple_pay", value),
    enable_google_pay: value => handleSettingChange("enable_google_pay", value),
    order_button_text: value => handleSettingChange("order_button_text", value),
    // PayID-specific fields
    payid_title: value => handleSettingChange("payid_title", value),
    payid_description: value => handleSettingChange("payid_description", value),
    payid_testmode: value => handleSettingChange("payid_testmode", value),
    payid_debug: value => handleSettingChange("payid_debug", value),
    payid_show_reference_field: value => handleSettingChange("payid_show_reference_field", value),
    static_bank_account_name: value => handleSettingChange("static_bank_account_name", value),
    static_bsb: value => handleSettingChange("static_bsb", value),
    static_account_number: value => handleSettingChange("static_account_number", value),
    payment_types: value => handleSettingChange("payment_types", value),
    expire_hours: value => handleSettingChange("expire_hours", value),
    account_name: value => handleSettingChange("account_name", value),
    instructions: value => handleSettingChange("instructions", value),
    // PayTo-specific fields
    payto_title: value => handleSettingChange("payto_title", value),
    payto_description: value => handleSettingChange("payto_description", value),
    payto_testmode: value => handleSettingChange("payto_testmode", value),
    payto_debug: value => handleSettingChange("payto_debug", value),
    payto_purpose: value => handleSettingChange("payto_purpose", value),
    payto_agreement_expiry_days: value => handleSettingChange("payto_agreement_expiry_days", value),
    payto_payee_type: value => handleSettingChange("payto_payee_type", value),
    payto_maximum_amount: value => handleSettingChange("payto_maximum_amount", parseFloat(value) || 1000),
    // PayID UI Style Settings
    payid_checkout_ui_styles: value => handleSettingChange("payid_checkout_ui_styles", value),
    // PayTo UI Style Settings
    payto_checkout_ui_styles: value => handleSettingChange("payto_checkout_ui_styles", value)
  }), [handleSettingChange]);

  // Enhanced form submission handling
  (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_1__.useEffect)(() => {
    // Enhanced form submission interception to prevent WooCommerce from reloading the page
    const handleWooCommerceFormSubmit = async event => {
      const form = event.target;

      // Multiple ways to detect the unified gateway form
      const isUnifiedGatewayForm = form && (form.querySelector("#monoova-payment-settings-container") || form.querySelector('input[name="woocommerce_monoova_unified_enabled"]') || form.querySelector('input[name*="monoova_unified"]') || window.location.href.includes("section=monoova_unified"));
      if (isUnifiedGatewayForm) {
        event.preventDefault();
        event.stopPropagation();

        // Find the submit button to manage its state
        const submitButton = form.querySelector('input[type="submit"], button[type="submit"], .button-primary');

        // Store original button value if not already stored
        if (submitButton && submitButton.value && !submitButton.getAttribute("data-original-value")) {
          submitButton.setAttribute("data-original-value", submitButton.value);
        }
        try {
          // Save settings via our React component
          await saveSettings();

          // Reset button state after successful save
          if (submitButton) {
            submitButton.classList.remove("is-busy");
            submitButton.disabled = false;
            if (submitButton.value) {
              submitButton.value = submitButton.getAttribute("data-original-value") || "Save changes";
            }
          }
        } catch (error) {
          // Reset button state even if save fails
          if (submitButton) {
            submitButton.classList.remove("is-busy");
            submitButton.disabled = false;
            if (submitButton.value) {
              submitButton.value = submitButton.getAttribute("data-original-value") || "Save changes";
            }
          }
          throw error; // Re-throw to maintain error handling
        }
        return false;
      }
    };

    // Add event listeners for form submissions
    document.addEventListener("submit", handleWooCommerceFormSubmit, true);

    // Also listen for the WooCommerce settings form submission event
    const wooCommerceForm = document.querySelector("form#mainform");
    if (wooCommerceForm) {
      wooCommerceForm.addEventListener("submit", handleWooCommerceFormSubmit);
    }
    return () => {
      document.removeEventListener("submit", handleWooCommerceFormSubmit, true);
      if (wooCommerceForm) {
        wooCommerceForm.removeEventListener("submit", handleWooCommerceFormSubmit);
      }
    };
  }, [saveSettings]); // Include saveSettings in dependencies

  const tabs = [{
    name: "general_settings",
    title: (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("General Settings", "monoova-payments-for-woocommerce"),
    content: /*#__PURE__*/React.createElement(GeneralSettingsTab, {
      settings: settings,
      saveNotice: saveNotice,
      onChangeHandlers: onChangeHandlers,
      setSaveNotice: setSaveNotice,
      validationErrors: validationErrors,
      webhookStatus: webhookStatus,
      onCheckWebhookStatus: checkWebhookStatus,
      onSubscribeToWebhooks: subscribeToWebhooks
    })
  }, {
    name: "payment_methods",
    title: (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("Payment methods", "monoova-payments-for-woocommerce"),
    content: /*#__PURE__*/React.createElement(PaymentMethodsTab, {
      settings: settings,
      saveNotice: saveNotice,
      onChangeHandlers: onChangeHandlers,
      setSaveNotice: setSaveNotice
    })
  }, {
    name: "card_settings",
    title: (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("Card settings", "monoova-payments-for-woocommerce"),
    content: /*#__PURE__*/React.createElement(CardSettingsTab, {
      settings: settings,
      saveNotice: saveNotice,
      onChangeHandlers: onChangeHandlers,
      setSaveNotice: setSaveNotice,
      handleSettingChange: handleSettingChange
    })
  }, {
    name: "payid_settings",
    title: (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("PayID settings", "monoova-payments-for-woocommerce"),
    content: /*#__PURE__*/React.createElement(PayIDSettingsTab, {
      settings: settings,
      saveNotice: saveNotice,
      onChangeHandlers: onChangeHandlers,
      setSaveNotice: setSaveNotice
      // Pass the new handler and state as props
      ,
      onGenerateAutomatcher: handleGenerateAutomatcher,
      isGenerating: isGenerating
    })
  }, {
    name: "payto_settings",
    title: (0,_wordpress_i18n__WEBPACK_IMPORTED_MODULE_3__.__)("PayTo settings", "monoova-payments-for-woocommerce"),
    content: /*#__PURE__*/React.createElement(PayToSettingsTab, {
      settings: settings,
      saveNotice: saveNotice,
      onChangeHandlers: onChangeHandlers,
      setSaveNotice: setSaveNotice,
      onGenerateAutomatcher: handleGenerateAutomatcher,
      isGenerating: isGenerating
    })
  }];
  return /*#__PURE__*/React.createElement("div", {
    className: "monoova-payment-settings"
  }, /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.TabPanel, {
    className: "monoova-settings-tabs",
    activeClass: "is-active",
    tabs: tabs
  }, tab => {
    return /*#__PURE__*/React.createElement(_wordpress_components__WEBPACK_IMPORTED_MODULE_2__.__experimentalVStack, {
      spacing: 6
    }, tab.content);
  }));
};
/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (PaymentSettings);

/***/ }),

/***/ "./node_modules/@babel/runtime/helpers/esm/extends.js":
/*!************************************************************!*\
  !*** ./node_modules/@babel/runtime/helpers/esm/extends.js ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (/* binding */ _extends)
/* harmony export */ });
function _extends() {
  return _extends = Object.assign ? Object.assign.bind() : function (n) {
    for (var e = 1; e < arguments.length; e++) {
      var t = arguments[e];
      for (var r in t) ({}).hasOwnProperty.call(t, r) && (n[r] = t[r]);
    }
    return n;
  }, _extends.apply(null, arguments);
}


/***/ }),

/***/ "@wordpress/components":
/*!************************************!*\
  !*** external ["wp","components"] ***!
  \************************************/
/***/ ((module) => {

module.exports = window["wp"]["components"];

/***/ }),

/***/ "@wordpress/element":
/*!*********************************!*\
  !*** external ["wp","element"] ***!
  \*********************************/
/***/ ((module) => {

module.exports = window["wp"]["element"];

/***/ }),

/***/ "@wordpress/i18n":
/*!******************************!*\
  !*** external ["wp","i18n"] ***!
  \******************************/
/***/ ((module) => {

module.exports = window["wp"]["i18n"];

/***/ })

/******/ 	});
/************************************************************************/
/******/ 	// The module cache
/******/ 	var __webpack_module_cache__ = {};
/******/ 	
/******/ 	// The require function
/******/ 	function __webpack_require__(moduleId) {
/******/ 		// Check if module is in cache
/******/ 		var cachedModule = __webpack_module_cache__[moduleId];
/******/ 		if (cachedModule !== undefined) {
/******/ 			return cachedModule.exports;
/******/ 		}
/******/ 		// Create a new module (and put it into the cache)
/******/ 		var module = __webpack_module_cache__[moduleId] = {
/******/ 			// no module.id needed
/******/ 			// no module.loaded needed
/******/ 			exports: {}
/******/ 		};
/******/ 	
/******/ 		// Execute the module function
/******/ 		__webpack_modules__[moduleId](module, module.exports, __webpack_require__);
/******/ 	
/******/ 		// Return the exports of the module
/******/ 		return module.exports;
/******/ 	}
/******/ 	
/************************************************************************/
/******/ 	/* webpack/runtime/compat get default export */
/******/ 	(() => {
/******/ 		// getDefaultExport function for compatibility with non-harmony modules
/******/ 		__webpack_require__.n = (module) => {
/******/ 			var getter = module && module.__esModule ?
/******/ 				() => (module['default']) :
/******/ 				() => (module);
/******/ 			__webpack_require__.d(getter, { a: getter });
/******/ 			return getter;
/******/ 		};
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/define property getters */
/******/ 	(() => {
/******/ 		// define getter functions for harmony exports
/******/ 		__webpack_require__.d = (exports, definition) => {
/******/ 			for(var key in definition) {
/******/ 				if(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {
/******/ 					Object.defineProperty(exports, key, { enumerable: true, get: definition[key] });
/******/ 				}
/******/ 			}
/******/ 		};
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/hasOwnProperty shorthand */
/******/ 	(() => {
/******/ 		__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))
/******/ 	})();
/******/ 	
/******/ 	/* webpack/runtime/make namespace object */
/******/ 	(() => {
/******/ 		// define __esModule on exports
/******/ 		__webpack_require__.r = (exports) => {
/******/ 			if(typeof Symbol !== 'undefined' && Symbol.toStringTag) {
/******/ 				Object.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });
/******/ 			}
/******/ 			Object.defineProperty(exports, '__esModule', { value: true });
/******/ 		};
/******/ 	})();
/******/ 	
/************************************************************************/
var __webpack_exports__ = {};
// This entry needs to be wrapped in an IIFE because it needs to be isolated against other modules in the chunk.
(() => {
/*!******************************************************!*\
  !*** ./assets/js/src/admin/payment-settings-init.js ***!
  \******************************************************/
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _wordpress_element__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @wordpress/element */ "@wordpress/element");
/* harmony import */ var _wordpress_element__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_wordpress_element__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var _payment_settings__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./payment-settings */ "./assets/js/src/admin/payment-settings.js");
/**
 * Monoova Payment Settings - Entry Point
 */




// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', function () {
  const container = document.getElementById('monoova-payment-settings-container');
  if (container) {
    const root = (0,_wordpress_element__WEBPACK_IMPORTED_MODULE_0__.createRoot)(container);
    root.render(/*#__PURE__*/React.createElement(_payment_settings__WEBPACK_IMPORTED_MODULE_1__["default"], null));
  }
});
})();

/******/ })()
;
//# sourceMappingURL=admin-payment-settings.js.map